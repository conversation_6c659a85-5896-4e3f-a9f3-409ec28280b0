<!DOCTYPE html>
<html lang="ru">
<head>
  <meta charset="UTF-8">
  <title>Калькулятор Pandora Box</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    h1 {
      color: #333;
      text-align: center;
      margin-bottom: 30px;
    }
    .block {
      margin-bottom: 25px;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 5px;
      background-color: #fafafa;
    }
    .form-row {
      display: flex;
      align-items: center;
      gap: 10px;
      flex-wrap: wrap;
    }
    label {
      font-weight: bold;
      min-width: 80px;
    }
    select, input {
      padding: 5px;
      border: 1px solid #ccc;
      border-radius: 3px;
    }
    #unitSelect {
      min-width: 200px;
    }
    #countInput {
      width: 80px;
    }
    button {
      padding: 8px 15px;
      background-color: #007bff;
      color: white;
      border: none;
      border-radius: 3px;
      cursor: pointer;
      font-size: 14px;
    }
    button:hover {
      background-color: #0056b3;
    }
    button.danger {
      background-color: #dc3545;
    }
    button.danger:hover {
      background-color: #c82333;
    }
    table {
      border-collapse: collapse;
      width: 100%;
      margin-top: 10px;
      background-color: white;
    }
    th, td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }
    th {
      background-color: #f8f9fa;
      font-weight: bold;
    }
    tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    .total-value {
      font-size: 18px;
      font-weight: bold;
      color: #28a745;
      margin: 15px 0;
    }
    .tolerance-control {
      margin: 10px 0;
    }
    .tolerance-control label {
      margin-right: 10px;
    }
    .tolerance-control input {
      width: 60px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Калькулятор Pandora Box</h1>

    <div class="block">
      <h3>Добавить юнит в охрану</h3>
      <div class="form-row">
        <label for="unitSelect">Юнит:</label>
        <select id="unitSelect"></select>
        <label for="countInput">Количество:</label>
        <input type="number" id="countInput" min="1" value="1">
        <button onclick="addUnit()">Добавить</button>
      </div>
    </div>

    <div class="block">
      <h3>Текущая охрана</h3>
      <table id="guardTable">
        <thead>
          <tr><th>Юнит</th><th>Ценность за штуку</th><th>Количество</th><th>Общая ценность</th><th>Действие</th></tr>
        </thead>
        <tbody></tbody>
      </table>
    </div>

    <div class="block">
      <h3>Расчет</h3>
      <div class="tolerance-control">
        <label for="toleranceInput">Допуск (%):</label>
        <input type="number" id="toleranceInput" min="0" max="50" value="10" step="1">
        <span>% (±10% означает диапазон от 90% до 110% от ценности охраны)</span>
      </div>
      <button onclick="calculate()">Рассчитать подходящие коробки</button>
      <div id="totalValue" class="total-value"></div>
    </div>

    <div class="block">
      <h3>Подходящие коробки Pandora</h3>
      <table id="resultTable">
        <thead>
          <tr><th>Ценность коробки</th><th>Тип награды</th><th>Параметры награды</th></tr>
        </thead>
        <tbody></tbody>
      </table>
    </div>
  </div>

  <!-- Зашитые юниты -->
  <script type="application/json" id="unitsData">
  {
    "array": [
      { "id": "godslayer", "squadValue": 774 },
      { "id": "angel", "squadValue": 3973 },
      { "id": "peasant", "squadValue": 150 },
      { "id": "dragon", "squadValue": 2783 },
      { "id": "dragon_upg", "squadValue": 3339 },
      { "id": "dragon_upg_alt", "squadValue": 3339 },
      { "id": "godslayer_upg", "squadValue": 929 },
      { "id": "godslayer_upg_alt", "squadValue": 929 },
      { "id": "hive_queen", "squadValue": 1963 },
      { "id": "hive_queen_upg", "squadValue": 2355 },
      { "id": "hive_queen_upg_alt", "squadValue": 2355 },
      { "id": "jaw", "squadValue": 1963 },
      { "id": "jaw_upg", "squadValue": 2355 },
      { "id": "jaw_upg_alt", "squadValue": 2355 },
      { "id": "lava_larva", "squadValue": 60 },
      { "id": "locust", "squadValue": 60 },
      { "id": "locust_upg", "squadValue": 72 },
      { "id": "locust_upg_alt", "squadValue": 72 },
      { "id": "olgoi", "squadValue": 387 },
      { "id": "olgoi_upg", "squadValue": 464 },
      { "id": "olgoi_upg_alt", "squadValue": 464 },
      { "id": "trick_demon", "squadValue": 387 },
      { "id": "trick_demon_upg", "squadValue": 464 },
      { "id": "trick_demon_upg_alt", "squadValue": 464 },
      { "id": "wasp", "squadValue": 193 },
      { "id": "wasp_upg", "squadValue": 232 },
      { "id": "wasp_upg_alt", "squadValue": 232 },
      { "id": "assassin", "squadValue": 1963 },
      { "id": "assassin_upg", "squadValue": 2355 },
      { "id": "assassin_upg_alt", "squadValue": 2355 },
      { "id": "black_dragon", "squadValue": 2783 },
      { "id": "black_dragon_upg", "squadValue": 3339 },
      { "id": "black_dragon_upg_alt", "squadValue": 3339 },
      { "id": "blade_dancer", "squadValue": 387 },
      { "id": "blade_dancer_upg", "squadValue": 464 },
      { "id": "blade_dancer_upg_alt", "squadValue": 464 },
      { "id": "hydra", "squadValue": 1963 },
      { "id": "hydra_upg", "squadValue": 2355 },
      { "id": "hydra_upg_alt", "squadValue": 2355 },
      { "id": "medusa", "squadValue": 774 },
      { "id": "medusa_upg", "squadValue": 929 },
      { "id": "medusa_upg_alt", "squadValue": 929 },
      { "id": "minos", "squadValue": 193 },
      { "id": "minos_upg", "squadValue": 232 },
      { "id": "minos_upg_alt", "squadValue": 232 },
      { "id": "trogl", "squadValue": 60 },
      { "id": "trogl_upg", "squadValue": 72 },
      { "id": "trogl_upg_alt", "squadValue": 72 },
      { "id": "angel_upg", "squadValue": 4767 },
      { "id": "angel_upg_alt", "squadValue": 4767 },
      { "id": "crossbowman", "squadValue": 193 },
      { "id": "crossbowman_upg", "squadValue": 232 },
      { "id": "crossbowman_upg_alt", "squadValue": 232 },
      { "id": "esquire", "squadValue": 60 },
      { "id": "esquire_upg", "squadValue": 72 },
      { "id": "esquire_upg_alt", "squadValue": 72 },
      { "id": "griffin", "squadValue": 387 },
      { "id": "griffin_upg", "squadValue": 464 },
      { "id": "griffin_upg_alt", "squadValue": 464 },
      { "id": "inquisitor", "squadValue": 1963 },
      { "id": "inquisitor_upg", "squadValue": 2355 },
      { "id": "inquisitor_upg_alt", "squadValue": 2355 },
      { "id": "lightweaver", "squadValue": 774 },
      { "id": "lightweaver_upg", "squadValue": 929 },
      { "id": "lightweaver_upg_alt", "squadValue": 929 },
      { "id": "sunlight_cavalry", "squadValue": 387 },
      { "id": "sunlight_cavalry_upg", "squadValue": 464 },
      { "id": "sunlight_cavalry_upg_alt", "squadValue": 464 },
      { "id": "druid", "squadValue": 774 },
      { "id": "druid_upg", "squadValue": 929 },
      { "id": "druid_upg_alt", "squadValue": 929 },
      { "id": "phoenix", "squadValue": 3973 },
      { "id": "twinkle", "squadValue": 60 },
      { "id": "twinkle_upg", "squadValue": 72 },
      { "id": "twinkle_upg_alt", "squadValue": 72 },
      { "id": "animated_armor", "squadValue": 387 },
      { "id": "avatar", "squadValue": 3973 },
      { "id": "avatar_nature", "squadValue": 3973 },
      { "id": "avatar_unfrozen", "squadValue": 3973 },
      { "id": "coatl", "squadValue": 1963 },
      { "id": "dragon_hunter", "squadValue": 774 },
      { "id": "fairy_dragon", "squadValue": 1963 },
      { "id": "giant_frog", "squadValue": 193 },
      { "id": "gorilla", "squadValue": 387 },
      { "id": "halfling", "squadValue": 60 },
      { "id": "kitten_horn", "squadValue": 193 },
      { "id": "lich_dragon", "squadValue": 2783 },
      { "id": "mech_guard", "squadValue": 774 },
      { "id": "obsidian_dragon", "squadValue": 2783 },
      { "id": "peasant_normal", "squadValue": 150 },
      { "id": "pixie", "squadValue": 60 },
      { "id": "primal_remnant", "squadValue": 1963 },
      { "id": "sentinel", "squadValue": 1963 },
      { "id": "star_child", "squadValue": 387 },
      { "id": "undead_peasant", "squadValue": 150 },
      { "id": "unicorn", "squadValue": 1963 },
      { "id": "avatar_of_war", "squadValue": 3973 },
      { "id": "avatar_of_war_upg", "squadValue": 4767 },
      { "id": "avatar_of_war_upg_alt", "squadValue": 4767 },
      { "id": "flicker", "squadValue": 60 },
      { "id": "flicker_upg", "squadValue": 72 },
      { "id": "flicker_upg_alt", "squadValue": 72 },
      { "id": "graverobber", "squadValue": 193 },
      { "id": "graverobber_upg", "squadValue": 232 },
      { "id": "graverobber_upg_alt", "squadValue": 232 },
      { "id": "lich", "squadValue": 1963 },
      { "id": "lich_upg", "squadValue": 2355 },
      { "id": "lich_upg_alt", "squadValue": 2355 },
      { "id": "pet", "squadValue": 387 },
      { "id": "pet_upg", "squadValue": 464 },
      { "id": "pet_upg_alt", "squadValue": 464 },
      { "id": "skeleton", "squadValue": 60 },
      { "id": "skeleton_upg", "squadValue": 72 },
      { "id": "skeleton_upg_alt", "squadValue": 72 },
      { "id": "vampire", "squadValue": 774 },
      { "id": "vampire_upg", "squadValue": 929 },
      { "id": "vampire_upg_alt", "squadValue": 929 },
      { "id": "arbitrator", "squadValue": 1963 },
      { "id": "frostworm_rider", "squadValue": 774 },
      { "id": "frostworm_rider_upg", "squadValue": 929 },
      { "id": "frostworm_rider_upg_alt", "squadValue": 929 },
      { "id": "succubus", "squadValue": 387 },
      { "id": "unfrozen_cultist", "squadValue": 193 },
      { "id": "unspeakable", "squadValue": 2783 }
    ]
  }
  </script>

  <!-- Зашитые коробки -->
  <script type="application/json" id="boxesData">
  {
    "array": [
      {
        "id": "pandora_box",
        "variants": [
          { "value": 5000, "rewardSet": { "rewards": [{ "rewardType": "SideResReward", "parameters": ["gold", "5000"] }] } },
          { "value": 10000, "rewardSet": { "rewards": [{ "rewardType": "SideResReward", "parameters": ["gold", "10000"] }] } },
          { "value": 15000, "rewardSet": { "rewards": [{ "rewardType": "SideResReward", "parameters": ["gold", "15000"] }] } },
          { "value": 20000, "rewardSet": { "rewards": [{ "rewardType": "SideResReward", "parameters": ["gold", "20000"] }] } },
          { "value": 6000, "rewardSet": { "rewards": [{ "rewardType": "HeroExpReward", "parameters": ["5000"] }] } },
          { "value": 12000, "rewardSet": { "rewards": [{ "rewardType": "HeroExpReward", "parameters": ["10000"] }] } },
          { "value": 18000, "rewardSet": { "rewards": [{ "rewardType": "HeroExpReward", "parameters": ["15000"] }] } },
          { "value": 24000, "rewardSet": { "rewards": [{ "rewardType": "HeroExpReward", "parameters": ["20000"] }] } },
          { "value": 5000, "rewardSet": { "rewards": [{ "rewardType": "HeroBoxUnitReward", "parameters": ["1"] }] } },
          { "value": 7500, "rewardSet": { "rewards": [{ "rewardType": "HeroBoxUnitReward", "parameters": ["2"] }] } },
          { "value": 10500, "rewardSet": { "rewards": [{ "rewardType": "HeroBoxUnitReward", "parameters": ["3"] }] } },
          { "value": 14000, "rewardSet": { "rewards": [{ "rewardType": "HeroBoxUnitReward", "parameters": ["4"] }] } },
          { "value": 18000, "rewardSet": { "rewards": [{ "rewardType": "HeroBoxUnitReward", "parameters": ["5"] }] } },
          { "value": 22500, "rewardSet": { "rewards": [{ "rewardType": "HeroBoxUnitReward", "parameters": ["6"] }] } },
          { "value": 27500, "rewardSet": { "rewards": [{ "rewardType": "HeroBoxUnitReward", "parameters": ["7"] }] } },
          { "value": 8000, "rewardSet": { "rewards": [{ "rewardType": "HeroStatsReward", "parameters": ["offence", "1", "defence", "1", "spellPower", "1", "intelligence", "1"] }] } },
          { "value": 16000, "rewardSet": { "rewards": [{ "rewardType": "HeroStatsReward", "parameters": ["offence", "2", "defence", "2", "spellPower", "2", "intelligence", "2"] }] } },
          { "value": 24000, "rewardSet": { "rewards": [{ "rewardType": "HeroStatsReward", "parameters": ["offence", "3", "defence", "3", "spellPower", "3", "intelligence", "3"] }] } },
          { "value": 32000, "rewardSet": { "rewards": [{ "rewardType": "HeroStatsReward", "parameters": ["offence", "4", "defence", "4", "spellPower", "4", "intelligence", "4"] }] } },
          { "value": 20000, "rewardSet": { "rewards": [{ "rewardType": "HeroMagicMassAdditionReward", "parameters": ["day", "any", "any"] }] } },
          { "value": 20000, "rewardSet": { "rewards": [{ "rewardType": "HeroMagicMassAdditionReward", "parameters": ["night", "any", "any"] }] } },
          { "value": 20000, "rewardSet": { "rewards": [{ "rewardType": "HeroMagicMassAdditionReward", "parameters": ["space", "any", "any"] }] } },
          { "value": 20000, "rewardSet": { "rewards": [{ "rewardType": "HeroMagicMassAdditionReward", "parameters": ["primal", "any", "any"] }] } },
          { "value": 5000, "rewardSet": { "rewards": [{ "rewardType": "HeroMagicMassAdditionReward", "parameters": ["any", "any", "1"] }] } },
          { "value": 10000, "rewardSet": { "rewards": [{ "rewardType": "HeroMagicMassAdditionReward", "parameters": ["any", "any", "2"] }] } },
          { "value": 15000, "rewardSet": { "rewards": [{ "rewardType": "HeroMagicMassAdditionReward", "parameters": ["any", "any", "3"] }] } },
          { "value": 20000, "rewardSet": { "rewards": [{ "rewardType": "HeroMagicMassAdditionReward", "parameters": ["any", "any", "4"] }] } },
          { "value": 25000, "rewardSet": { "rewards": [{ "rewardType": "HeroMagicMassAdditionReward", "parameters": ["any", "any", "5"] }] } }
        ]
      }
    ]
  }
  </script>

  <script>
    const units = JSON.parse(document.getElementById('unitsData').textContent).array;
    const boxes = JSON.parse(document.getElementById('boxesData').textContent).array;

    const guard = [];

    // заполняем селект юнитов
    const unitSelect = document.getElementById('unitSelect');

    // Сортируем юниты по названию для удобства
    const sortedUnits = units.sort((a, b) => a.id.localeCompare(b.id));

    sortedUnits.forEach(u => {
      const opt = document.createElement('option');
      opt.value = u.id;
      opt.textContent = `${u.id} (ценность: ${u.squadValue})`;
      unitSelect.appendChild(opt);
    });

    function addUnit() {
      const unitId = unitSelect.value;
      const count = parseInt(document.getElementById('countInput').value);
      if (!unitId || isNaN(count) || count <= 0) {
        alert('Выберите юнит и введите корректное количество');
        return;
      }

      // Проверяем, есть ли уже такой юнит в охране
      const existingIndex = guard.findIndex(g => g.id === unitId);
      if (existingIndex !== -1) {
        // Если есть, увеличиваем количество
        guard[existingIndex].count += count;
      } else {
        // Если нет, добавляем новый
        guard.push({ id: unitId, count });
      }

      renderGuard();
      // Сбрасываем поле количества
      document.getElementById('countInput').value = 1;
    }

    function removeUnit(index) {
      guard.splice(index, 1);
      renderGuard();
    }

    function renderGuard() {
      const tbody = document.querySelector('#guardTable tbody');
      tbody.innerHTML = '';

      if (guard.length === 0) {
        const tr = document.createElement('tr');
        tr.innerHTML = '<td colspan="5" style="text-align: center; color: #666;">Охрана не добавлена</td>';
        tbody.appendChild(tr);
        return;
      }

      guard.forEach((g, i) => {
        const unit = units.find(u => u.id === g.id);
        const unitValue = unit ? unit.squadValue : 0;
        const totalValue = unitValue * g.count;

        const tr = document.createElement('tr');
        tr.innerHTML = `
          <td>${g.id}</td>
          <td>${unitValue}</td>
          <td>${g.count}</td>
          <td><strong>${totalValue}</strong></td>
          <td><button class="danger" onclick="removeUnit(${i})">Удалить</button></td>
        `;
        tbody.appendChild(tr);
      });
    }

    function calculate() {
      if (guard.length === 0) {
        alert('Добавьте юниты в охрану перед расчетом');
        return;
      }

      let total = 0;
      guard.forEach(g => {
        const unit = units.find(u => u.id === g.id);
        if (unit) total += g.count * unit.squadValue;
      });

      const tolerancePercent = parseInt(document.getElementById('toleranceInput').value) || 10;
      const tolerance = tolerancePercent / 100;

      document.getElementById('totalValue').innerHTML = `
        <strong>Общая ценность охраны: ${total}</strong><br>
        <small>Диапазон поиска: ${Math.round(total * (1 - tolerance))} - ${Math.round(total * (1 + tolerance))} (±${tolerancePercent}%)</small>
      `;

      const tbody = document.querySelector('#resultTable tbody');
      tbody.innerHTML = '';

      const minVal = total * (1 - tolerance);
      const maxVal = total * (1 + tolerance);

      const matchingBoxes = [];

      boxes.forEach(box => {
        box.variants.forEach(variant => {
          if (variant.value >= minVal && variant.value <= maxVal) {
            variant.rewardSet.rewards.forEach(r => {
              matchingBoxes.push({
                value: variant.value,
                rewardType: r.rewardType,
                parameters: r.parameters
              });
            });
          }
        });
      });

      if (matchingBoxes.length === 0) {
        const tr = document.createElement('tr');
        tr.innerHTML = '<td colspan="3" style="text-align: center; color: #666;">Подходящие коробки не найдены. Попробуйте увеличить допуск.</td>';
        tbody.appendChild(tr);
        return;
      }

      // Сортируем по ценности
      matchingBoxes.sort((a, b) => a.value - b.value);

      matchingBoxes.forEach(box => {
        const tr = document.createElement('tr');
        const rewardText = formatRewardText(box.rewardType, box.parameters);
        tr.innerHTML = `
          <td><strong>${box.value}</strong></td>
          <td>${formatRewardType(box.rewardType)}</td>
          <td>${rewardText}</td>
        `;
        tbody.appendChild(tr);
      });
    }

    function formatRewardType(rewardType) {
      const types = {
        'SideResReward': 'Ресурсы',
        'HeroExpReward': 'Опыт героя',
        'HeroBoxUnitReward': 'Случайный юнит',
        'HeroStatsReward': 'Характеристики героя',
        'HeroMagicMassAdditionReward': 'Заклинания'
      };
      return types[rewardType] || rewardType;
    }

    function formatRewardText(rewardType, parameters) {
      switch (rewardType) {
        case 'SideResReward':
          return `${parameters[1]} ${parameters[0]}`;
        case 'HeroExpReward':
          return `${parameters[0]} опыта`;
        case 'HeroBoxUnitReward':
          return `${parameters[0]} случайный юнит`;
        case 'HeroStatsReward':
          const stats = [];
          for (let i = 0; i < parameters.length; i += 2) {
            stats.push(`${parameters[i]}: +${parameters[i + 1]}`);
          }
          return stats.join(', ');
        case 'HeroMagicMassAdditionReward':
          if (parameters[2] && parameters[2] !== 'any') {
            return `${parameters[2]} заклинание уровня ${parameters[2]}`;
          } else if (parameters[0] !== 'any') {
            return `Заклинание школы ${parameters[0]}`;
          } else {
            return 'Случайное заклинание';
          }
        default:
          return parameters.join(', ');
      }
    }

    // Инициализация при загрузке страницы
    document.addEventListener('DOMContentLoaded', function() {
      renderGuard();
    });
  </script>
</body>
</html>
