<!DOCTYPE html>
<html lang="ru">
<head>
  <meta charset="UTF-8">
  <title>Pandora Box Calculator</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    h1 { font-size: 20px; }
    .block { margin-bottom: 20px; }
    table { border-collapse: collapse; width: 100%; margin-top: 10px; }
    th, td { border: 1px solid #ccc; padding: 6px; text-align: left; }
    button { margin-left: 10px; }
  </style>
</head>
<body>
  <h1>Калькулятор Pandora Box</h1>

  <div class="block">
    <label for="unitSelect">Юнит:</label>
    <select id="unitSelect"></select>
    <label for="countInput">Количество:</label>
    <input type="number" id="countInput" min="1" value="1">
    <button onclick="addUnit()">Добавить</button>
  </div>

  <div class="block">
    <h2>Охрана</h2>
    <table id="guardTable">
      <thead>
        <tr><th>Юнит</th><th>Количество</th><th>Удалить</th></tr>
      </thead>
      <tbody></tbody>
    </table>
  </div>

  <div class="block">
    <button onclick="calculate()">Рассчитать</button>
    <p id="totalValue"></p>
  </div>

  <div class="block">
    <h2>Подходящие коробки</h2>
    <table id="resultTable">
      <thead>
        <tr><th>Value</th><th>Reward Type</th><th>Parameters</th></tr>
      </thead>
      <tbody></tbody>
    </table>
  </div>

  <!-- Зашитые юниты -->
  <script type="application/json" id="unitsData">
  {
    "array": [
      { "id": "godslayer", "squadValue": 774 },
      { "id": "pikeman", "squadValue": 60 },
      { "id": "archer", "squadValue": 100 }
    ]
  }
  </script>

  <!-- Зашитые коробки -->
  <script type="application/json" id="boxesData">
  {
    "array": [
      {
        "id": "pandora_box",
        "variants": [
          {
            "value": 5000,
            "rewardSet": {
              "rewards": [
                { "rewardType": "SideResReward", "parameters": ["gold", "5000"] }
              ]
            }
          },
          {
            "value": 10000,
            "rewardSet": {
              "rewards": [
                { "rewardType": "SideResReward", "parameters": ["wood", "20"] }
              ]
            }
          }
        ]
      }
    ]
  }
  </script>

  <script>
    const units = JSON.parse(document.getElementById('unitsData').textContent).array;
    const boxes = JSON.parse(document.getElementById('boxesData').textContent).array;

    const guard = [];

    // заполняем селект юнитов
    const unitSelect = document.getElementById('unitSelect');
    units.forEach(u => {
      const opt = document.createElement('option');
      opt.value = u.id;
      opt.textContent = `${u.id} (value: ${u.squadValue})`;
      unitSelect.appendChild(opt);
    });

    function addUnit() {
      const unitId = unitSelect.value;
      const count = parseInt(document.getElementById('countInput').value);
      if (!unitId || isNaN(count) || count <= 0) return;
      guard.push({ id: unitId, count });
      renderGuard();
    }

    function removeUnit(index) {
      guard.splice(index, 1);
      renderGuard();
    }

    function renderGuard() {
      const tbody = document.querySelector('#guardTable tbody');
      tbody.innerHTML = '';
      guard.forEach((g, i) => {
        const tr = document.createElement('tr');
        tr.innerHTML = `<td>${g.id}</td><td>${g.count}</td><td><button onclick="removeUnit(${i})">X</button></td>`;
        tbody.appendChild(tr);
      });
    }

    function calculate() {
      let total = 0;
      guard.forEach(g => {
        const unit = units.find(u => u.id === g.id);
        if (unit) total += g.count * unit.squadValue;
      });

      document.getElementById('totalValue').textContent = `Ценность охраны: ${total}`;

      const tbody = document.querySelector('#resultTable tbody');
      tbody.innerHTML = '';

      const tolerance = 0.1; // 10%
      const minVal = total * (1 - tolerance);
      const maxVal = total * (1 + tolerance);

      boxes.forEach(box => {
        box.variants.forEach(variant => {
          if (variant.value >= minVal && variant.value <= maxVal) {
            variant.rewardSet.rewards.forEach(r => {
              const tr = document.createElement('tr');
              tr.innerHTML = `<td>${variant.value}</td><td>${r.rewardType}</td><td>${r.parameters.join(', ')}</td>`;
              tbody.appendChild(tr);
            });
          }
        });
      });
    }
  </script>
</body>
</html>
