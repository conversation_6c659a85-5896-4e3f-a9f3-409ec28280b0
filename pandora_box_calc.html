<!DOCTYPE html>
<html lang="ru">
<head>
  <meta charset="UTF-8">
  <title>Калькулятор Pandora Box</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    h1 {
      color: #333;
      text-align: center;
      margin-bottom: 30px;
    }
    .block {
      margin-bottom: 25px;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 5px;
      background-color: #fafafa;
    }
    .form-row {
      display: flex;
      align-items: center;
      gap: 10px;
      flex-wrap: wrap;
    }
    label {
      font-weight: bold;
      min-width: 80px;
    }
    select, input {
      padding: 5px;
      border: 1px solid #ccc;
      border-radius: 3px;
    }
    #unitSelect {
      min-width: 200px;
    }
    #countInput {
      width: 80px;
    }
    button {
      padding: 8px 15px;
      background-color: #007bff;
      color: white;
      border: none;
      border-radius: 3px;
      cursor: pointer;
      font-size: 14px;
    }
    button:hover {
      background-color: #0056b3;
    }
    button.danger {
      background-color: #dc3545;
    }
    button.danger:hover {
      background-color: #c82333;
    }
    table {
      border-collapse: collapse;
      width: 100%;
      margin-top: 10px;
      background-color: white;
    }
    th, td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }
    th {
      background-color: #f8f9fa;
      font-weight: bold;
    }
    tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    .total-value {
      font-size: 18px;
      font-weight: bold;
      color: #28a745;
      margin: 15px 0;
    }
    .tolerance-control {
      margin: 10px 0;
    }
    .tolerance-control label {
      margin-right: 10px;
    }
    .tolerance-control input {
      width: 60px;
    }
    .mode-selection {
      margin-bottom: 15px;
      padding: 10px;
      background-color: #e9ecef;
      border-radius: 5px;
    }
    .mode-selection label {
      margin-right: 20px;
      font-weight: normal;
      cursor: pointer;
    }
    .mode-selection input[type="radio"] {
      margin-right: 5px;
    }
    #countRangeSelect {
      min-width: 150px;
    }
    #exactMode, #rangeMode {
      display: inline-flex;
      align-items: center;
      gap: 10px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Калькулятор Pandora Box</h1>

    <div class="block">
      <h3>Добавить юнит в охрану</h3>

      <div class="mode-selection">
        <label>
          <input type="radio" name="inputMode" value="exact" checked onchange="toggleInputMode()">
          Точно (точное количество)
        </label>
        <label>
          <input type="radio" name="inputMode" value="range" onchange="toggleInputMode()">
          Разброс (диапазон количества)
        </label>
      </div>

      <div class="form-row">
        <label for="fractionFilter">Фракция:</label>
        <select id="fractionFilter" onchange="filterUnits()">
          <option value="all">Все фракции</option>
        </select>
        <label for="unitSelect">Юнит:</label>
        <select id="unitSelect"></select>

        <div id="exactMode">
          <label for="countInput">Количество:</label>
          <input type="number" id="countInput" min="1" value="1">
        </div>

        <div id="rangeMode" style="display: none;">
          <label for="countRangeSelect">Количество:</label>
          <select id="countRangeSelect">
            <option value="1-4">1–4</option>
            <option value="5-9">5–9</option>
            <option value="10-19">10–19</option>
            <option value="20-49">20–49</option>
            <option value="50-99">50–99</option>
            <option value="100-249">100–249</option>
            <option value="250-499">250–499</option>
            <option value="500-999">500–999</option>
            <option value="1000+">1000+</option>
          </select>
        </div>

        <button onclick="addUnit()">Добавить</button>
      </div>
    </div>

    <div class="block">
      <h3>Текущая охрана</h3>
      <table id="guardTable">
        <thead>
          <tr><th>Юнит</th><th>Ценность за штуку</th><th>Количество</th><th>Общая ценность</th><th>Действие</th></tr>
        </thead>
        <tbody></tbody>
      </table>
    </div>

    <div class="block">
      <h3>Расчет</h3>
      <div id="toleranceControl" class="tolerance-control">
        <label for="toleranceInput">Допуск (%):</label>
        <input type="number" id="toleranceInput" min="0" max="50" value="10" step="1">
        <span>% (±10% означает диапазон от 90% до 110% от ценности охраны)</span>
      </div>
      <button onclick="calculate()">Рассчитать подходящие коробки</button>
      <div id="totalValue" class="total-value"></div>
    </div>

    <div class="block">
      <h3>Подходящие коробки Pandora</h3>
      <table id="resultTable">
        <thead>
          <tr><th>Ценность коробки</th><th>Тип награды</th><th>Параметры награды</th></tr>
        </thead>
        <tbody></tbody>
      </table>
    </div>
  </div>

  <!-- Зашитые юниты -->
  <script type="application/json" id="unitsData">
  {
    "array": [
      { "id": "angel", "squadValue": 3973, "tier": 7, "fraction": "human" },
      { "id": "angel_upg", "squadValue": 6870, "tier": 7, "fraction": "human" },
      { "id": "angel_upg_alt", "squadValue": 6870, "tier": 7, "fraction": "human" },
      { "id": "animated_armor", "squadValue": 250, "tier": 3, "fraction": "neutral" },
      { "id": "arbitrator", "squadValue": 1201, "tier": 6, "fraction": "unfrozen" },
      { "id": "assassin", "squadValue": 92, "tier": 2, "fraction": "dungeon" },
      { "id": "assassin_upg", "squadValue": 124, "tier": 2, "fraction": "dungeon" },
      { "id": "assassin_upg_alt", "squadValue": 124, "tier": 2, "fraction": "dungeon" },
      { "id": "avatar", "squadValue": 250, "tier": 3, "fraction": "neutral" },
      { "id": "avatar_nature", "squadValue": 250, "tier": 3, "fraction": "neutral" },
      { "id": "avatar_of_war", "squadValue": 824, "tier": 6, "fraction": "undead" },
      { "id": "avatar_of_war_upg", "squadValue": 1316, "tier": 6, "fraction": "undead" },
      { "id": "avatar_of_war_upg_alt", "squadValue": 1316, "tier": 6, "fraction": "undead" },
      { "id": "avatar_unfrozen", "squadValue": 250, "tier": 3, "fraction": "neutral" },
      { "id": "black_dragon", "squadValue": 4981, "tier": 7, "fraction": "dungeon" },
      { "id": "black_dragon_upg", "squadValue": 8660, "tier": 7, "fraction": "dungeon" },
      { "id": "black_dragon_upg_alt", "squadValue": 8660, "tier": 7, "fraction": "dungeon" },
      { "id": "blade_dancer", "squadValue": 143, "tier": 3, "fraction": "dungeon" },
      { "id": "blade_dancer_upg", "squadValue": 200, "tier": 3, "fraction": "dungeon" },
      { "id": "blade_dancer_upg_alt", "squadValue": 200, "tier": 3, "fraction": "dungeon" },
      { "id": "coatl", "squadValue": 1650, "tier": 6, "fraction": "neutral" },
      { "id": "crossbowman", "squadValue": 100, "tier": 2, "fraction": "human" },
      { "id": "crossbowman_upg", "squadValue": 136, "tier": 2, "fraction": "human" },
      { "id": "crossbowman_upg_alt", "squadValue": 136, "tier": 2, "fraction": "human" },
      { "id": "dragon", "squadValue": 5155, "tier": 7, "fraction": "neutral" },
      { "id": "dragon_hunter", "squadValue": 1650, "tier": 6, "fraction": "neutral" },
      { "id": "dragon_upg", "squadValue": 8985, "tier": 7, "fraction": "neutral" },
      { "id": "dragon_upg_alt", "squadValue": 8985, "tier": 7, "fraction": "neutral" },
      { "id": "druid", "squadValue": 506, "tier": 5, "fraction": "nature" },
      { "id": "druid_upg", "squadValue": 770, "tier": 5, "fraction": "nature" },
      { "id": "druid_upg_alt", "squadValue": 770, "tier": 5, "fraction": "nature" },
      { "id": "esquire", "squadValue": 63, "tier": 1, "fraction": "human" },
      { "id": "esquire_upg", "squadValue": 83, "tier": 1, "fraction": "human" },
      { "id": "esquire_upg_alt", "squadValue": 83, "tier": 1, "fraction": "human" },
      { "id": "fairy_dragon", "squadValue": 850, "tier": 5, "fraction": "neutral" },
      { "id": "flicker", "squadValue": 77, "tier": 2, "fraction": "undead" },
      { "id": "flicker_upg", "squadValue": 105, "tier": 2, "fraction": "undead" },
      { "id": "flicker_upg_alt", "squadValue": 105, "tier": 2, "fraction": "undead" },
      { "id": "frostworm_rider", "squadValue": 192, "tier": 3, "fraction": "unfrozen" },
      { "id": "frostworm_rider_upg", "squadValue": 267, "tier": 3, "fraction": "unfrozen" },
      { "id": "frostworm_rider_upg_alt", "squadValue": 267, "tier": 3, "fraction": "unfrozen" },
      { "id": "giant_frog", "squadValue": 850, "tier": 5, "fraction": "neutral" },
      { "id": "godslayer", "squadValue": 774, "tier": 5, "fraction": "demon" },
      { "id": "godslayer_upg", "squadValue": 1169, "tier": 5, "fraction": "demon" },
      { "id": "godslayer_upg_alt", "squadValue": 1169, "tier": 5, "fraction": "demon" },
      { "id": "gorilla", "squadValue": 250, "tier": 3, "fraction": "neutral" },
      { "id": "graverobber", "squadValue": 252, "tier": 4, "fraction": "undead" },
      { "id": "graverobber_upg", "squadValue": 362, "tier": 4, "fraction": "undead" },
      { "id": "graverobber_upg_alt", "squadValue": 362, "tier": 4, "fraction": "undead" },
      { "id": "griffin", "squadValue": 225, "tier": 3, "fraction": "human" },
      { "id": "griffin_upg", "squadValue": 315, "tier": 3, "fraction": "human" },
      { "id": "griffin_upg_alt", "squadValue": 315, "tier": 3, "fraction": "human" },
      { "id": "halfling", "squadValue": 150, "tier": 2, "fraction": "neutral" },
      { "id": "hive_queen", "squadValue": 3449, "tier": 7, "fraction": "demon" },
      { "id": "hive_queen_upg", "squadValue": 6045, "tier": 7, "fraction": "demon" },
      { "id": "hive_queen_upg_alt", "squadValue": 6045, "tier": 7, "fraction": "demon" },
      { "id": "hydra", "squadValue": 1390, "tier": 6, "fraction": "dungeon" },
      { "id": "hydra_upg", "squadValue": 2205, "tier": 6, "fraction": "dungeon" },
      { "id": "hydra_upg_alt", "squadValue": 2205, "tier": 6, "fraction": "dungeon" },
      { "id": "inquisitor", "squadValue": 1116, "tier": 6, "fraction": "human" },
      { "id": "inquisitor_upg", "squadValue": 1771, "tier": 6, "fraction": "human" },
      { "id": "inquisitor_upg_alt", "squadValue": 1771, "tier": 6, "fraction": "human" },
      { "id": "jaw", "squadValue": 244, "tier": 4, "fraction": "demon" },
      { "id": "jaw_upg", "squadValue": 359, "tier": 4, "fraction": "demon" },
      { "id": "jaw_upg_alt", "squadValue": 359, "tier": 4, "fraction": "demon" },
      { "id": "kitten_horn", "squadValue": 550, "tier": 4, "fraction": "neutral" },
      { "id": "lava_larva", "squadValue": 100, "tier": 4, "fraction": "demon" },
      { "id": "lich", "squadValue": 380, "tier": 5, "fraction": "undead" },
      { "id": "lich_dragon", "squadValue": 30000, "tier": 8, "fraction": "neutral" },
      { "id": "lich_upg", "squadValue": 562, "tier": 5, "fraction": "undead" },
      { "id": "lich_upg_alt", "squadValue": 562, "tier": 5, "fraction": "undead" },
      { "id": "lightweaver", "squadValue": 263, "tier": 4, "fraction": "human" },
      { "id": "lightweaver_upg", "squadValue": 385, "tier": 4, "fraction": "human" },
      { "id": "lightweaver_upg_alt", "squadValue": 385, "tier": 4, "fraction": "human" },
      { "id": "locust", "squadValue": 119, "tier": 2, "fraction": "demon" },
      { "id": "locust_upg", "squadValue": 160, "tier": 2, "fraction": "demon" },
      { "id": "locust_upg_alt", "squadValue": 160, "tier": 2, "fraction": "demon" },
      { "id": "mech_guard", "squadValue": 1650, "tier": 6, "fraction": "neutral" },
      { "id": "medusa", "squadValue": 424, "tier": 5, "fraction": "dungeon" },
      { "id": "medusa_upg", "squadValue": 643, "tier": 5, "fraction": "dungeon" },
      { "id": "medusa_upg_alt", "squadValue": 643, "tier": 5, "fraction": "dungeon" },
      { "id": "minos", "squadValue": 403, "tier": 4, "fraction": "dungeon" },
      { "id": "minos_upg", "squadValue": 577, "tier": 4, "fraction": "dungeon" },
      { "id": "minos_upg_alt", "squadValue": 577, "tier": 4, "fraction": "dungeon" },
      { "id": "obsidian_dragon", "squadValue": 7500, "tier": 7, "fraction": "neutral" },
      { "id": "olgoi", "squadValue": 1384, "tier": 6, "fraction": "demon" },
      { "id": "olgoi_upg", "squadValue": 2234, "tier": 6, "fraction": "demon" },
      { "id": "olgoi_upg_alt", "squadValue": 2234, "tier": 6, "fraction": "demon" },
      { "id": "peasant", "squadValue": 150, "tier": 2, "fraction": "neutral" },
      { "id": "peasant_normal", "squadValue": 150, "tier": 2, "fraction": "neutral" },
      { "id": "pet", "squadValue": 113, "tier": 3, "fraction": "undead" },
      { "id": "pet_upg", "squadValue": 160, "tier": 3, "fraction": "undead" },
      { "id": "pet_upg_alt", "squadValue": 160, "tier": 3, "fraction": "undead" },
      { "id": "phoenix", "squadValue": 4415, "tier": 7, "fraction": "nature" },
      { "id": "pixie", "squadValue": 100, "tier": 4, "fraction": "neutral" },
      { "id": "primal_remnant", "squadValue": 550, "tier": 4, "fraction": "neutral" },
      { "id": "sentinel", "squadValue": 7500, "tier": 7, "fraction": "neutral" },
      { "id": "skeleton", "squadValue": 32, "tier": 1, "fraction": "undead" },
      { "id": "skeleton_upg", "squadValue": 41, "tier": 1, "fraction": "undead" },
      { "id": "skeleton_upg_alt", "squadValue": 41, "tier": 1, "fraction": "undead" },
      { "id": "star_child", "squadValue": 550, "tier": 4, "fraction": "neutral" },
      { "id": "succubus", "squadValue": 675, "tier": 5, "fraction": "unfrozen" },
      { "id": "sunlight_cavalry", "squadValue": 808, "tier": 5, "fraction": "human" },
      { "id": "sunlight_cavalry_upg", "squadValue": 1194, "tier": 5, "fraction": "human" },
      { "id": "sunlight_cavalry_upg_alt", "squadValue": 1194, "tier": 5, "fraction": "human" },
      { "id": "trick_demon", "squadValue": 63, "tier": 1, "fraction": "demon" },
      { "id": "trick_demon_upg", "squadValue": 83, "tier": 1, "fraction": "demon" },
      { "id": "trick_demon_upg_alt", "squadValue": 81, "tier": 1, "fraction": "demon" },
      { "id": "trogl", "squadValue": 37, "tier": 1, "fraction": "dungeon" },
      { "id": "trogl_upg", "squadValue": 48, "tier": 1, "fraction": "dungeon" },
      { "id": "trogl_upg_alt", "squadValue": 48, "tier": 1, "fraction": "dungeon" },
      { "id": "twinkle", "squadValue": 119, "tier": 2, "fraction": "nature" },
      { "id": "twinkle_upg", "squadValue": 163, "tier": 2, "fraction": "nature" },
      { "id": "twinkle_upg_alt", "squadValue": 163, "tier": 2, "fraction": "nature" },
      { "id": "undead_peasant", "squadValue": 1, "tier": 2, "fraction": "neutral" },
      { "id": "unfrozen_cultist", "squadValue": 112, "tier": 2, "fraction": "unfrozen" },
      { "id": "unicorn", "squadValue": 550, "tier": 4, "fraction": "neutral" },
      { "id": "unspeakable", "squadValue": 4124, "tier": 7, "fraction": "unfrozen" },
      { "id": "vampire", "squadValue": 1981, "tier": 7, "fraction": "undead" },
      { "id": "vampire_upg", "squadValue": 3447, "tier": 7, "fraction": "undead" },
      { "id": "vampire_upg_alt", "squadValue": 3447, "tier": 7, "fraction": "undead" },
      { "id": "wasp", "squadValue": 180, "tier": 3, "fraction": "demon" },
      { "id": "wasp_upg", "squadValue": 255, "tier": 3, "fraction": "demon" },
      { "id": "wasp_upg_alt", "squadValue": 255, "tier": 3, "fraction": "demon" }
    ]
  }
  </script>

  <!-- Зашитые коробки -->
  <script type="application/json" id="boxesData">
  {
  "array": [
    {
      "value": 1500,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "HeroBoxUnitsReward",
            "parameters": [
              "lich",
              "flicker",
              "avatar_of_war"
            ]
          }
        ]
      }
    },
    {
      "value": 2000,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "MovePointsAdditionReward",
            "parameters": [
              "300"
            ]
          }
        ]
      }
    },
    {
      "value": 2000,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "ManaAdditionReward",
            "parameters": [
              "1000",
              "true"
            ]
          }
        ]
      }
    },
    {
      "value": 2000,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "ManaPercentSettingReward",
            "parameters": [
              "0.8"
            ]
          }
        ]
      }
    },
    {
      "value": 2000,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "HeroMagicAdditionReward",
            "parameters": [
              "night_2_magic_web",
              "primal_1_magic_thunderbolt",
              "primal_4_magic_fire_globe",
              "day_1_magic_healing_water",
              "day_8_magic_taunt",
              "primal_7_magic_wall_of_flame",
              "night_11_magic_vulnerability",
              "day_12_magic_radiant_armor",
              "night_16_magic_shadow_army"
            ]
          }
        ]
      }
    },
    {
      "value": 2000,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "SideExpReward",
            "parameters": [
              "1000"
            ]
          }
        ]
      }
    },
    {
      "value": 2000,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "SideExpToLevelUpReward",
            "parameters": [
              "false"
            ]
          }
        ]
      }
    },
    {
      "value": 2000,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "HeroRandomItemsReward",
            "parameters": [
              "common"
            ]
          }
        ]
      }
    },
    {
      "value": 5000,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "SideResReward",
            "parameters": [
              "gold",
              "5000"
            ]
          }
        ]
      }
    },
    {
      "value": 5000,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "HeroBoxUnitReward",
            "parameters": [
              "1"
            ]
          }
        ]
      }
    },
    {
      "value": 5000,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "HeroMagicMassAdditionReward",
            "parameters": [
              "any",
              "any",
              "1"
            ]
          }
        ]
      }
    },
    {
      "value": 6000,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "HeroExpReward",
            "parameters": [
              "5000"
            ]
          }
        ]
      }
    },
    {
      "value": 7500,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "HeroBoxUnitReward",
            "parameters": [
              "2"
            ]
          }
        ]
      }
    },
    {
      "value": 8000,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "HeroStatsReward",
            "parameters": [
              "offence",
              "1",
              "defence",
              "1",
              "spellPower",
              "1",
              "intelligence",
              "1"
            ]
          }
        ]
      }
    },
    {
      "value": 10000,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "SideResReward",
            "parameters": [
              "gold",
              "10000"
            ]
          }
        ]
      }
    },
    {
      "value": 10000,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "HeroMagicMassAdditionReward",
            "parameters": [
              "any",
              "any",
              "2"
            ]
          }
        ]
      }
    },
    {
      "value": 10500,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "HeroBoxUnitReward",
            "parameters": [
              "3"
            ]
          }
        ]
      }
    },
    {
      "value": 12000,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "HeroExpReward",
            "parameters": [
              "10000"
            ]
          }
        ]
      }
    },
    {
      "value": 14000,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "HeroBoxUnitReward",
            "parameters": [
              "4"
            ]
          }
        ]
      }
    },
    {
      "value": 15000,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "SideResReward",
            "parameters": [
              "gold",
              "15000"
            ]
          }
        ]
      }
    },
    {
      "value": 15000,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "HeroMagicMassAdditionReward",
            "parameters": [
              "any",
              "any",
              "3"
            ]
          }
        ]
      }
    },
    {
      "value": 16000,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "HeroStatsReward",
            "parameters": [
              "offence",
              "2",
              "defence",
              "2",
              "spellPower",
              "2",
              "intelligence",
              "2"
            ]
          }
        ]
      }
    },
    {
      "value": 18000,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "HeroExpReward",
            "parameters": [
              "15000"
            ]
          }
        ]
      }
    },
    {
      "value": 18000,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "HeroBoxUnitReward",
            "parameters": [
              "5"
            ]
          }
        ]
      }
    },
    {
      "value": 20000,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "SideResReward",
            "parameters": [
              "gold",
              "20000"
            ]
          }
        ]
      }
    },
    {
      "value": 20000,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "HeroMagicMassAdditionReward",
            "parameters": [
              "day",
              "any",
              "any"
            ]
          }
        ]
      }
    },
    {
      "value": 20000,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "HeroMagicMassAdditionReward",
            "parameters": [
              "night",
              "any",
              "any"
            ]
          }
        ]
      }
    },
    {
      "value": 20000,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "HeroMagicMassAdditionReward",
            "parameters": [
              "space",
              "any",
              "any"
            ]
          }
        ]
      }
    },
    {
      "value": 20000,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "HeroMagicMassAdditionReward",
            "parameters": [
              "primal",
              "any",
              "any"
            ]
          }
        ]
      }
    },
    {
      "value": 20000,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "HeroMagicMassAdditionReward",
            "parameters": [
              "any",
              "any",
              "4"
            ]
          }
        ]
      }
    },
    {
      "value": 22500,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "HeroBoxUnitReward",
            "parameters": [
              "6"
            ]
          }
        ]
      }
    },
    {
      "value": 24000,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "HeroExpReward",
            "parameters": [
              "20000"
            ]
          }
        ]
      }
    },
    {
      "value": 24000,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "HeroStatsReward",
            "parameters": [
              "offence",
              "3",
              "defence",
              "3",
              "spellPower",
              "3",
              "intelligence",
              "3"
            ]
          }
        ]
      }
    },
    {
      "value": 25000,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "HeroMagicMassAdditionReward",
            "parameters": [
              "any",
              "any",
              "5"
            ]
          }
        ]
      }
    },
    {
      "value": 27500,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "HeroBoxUnitReward",
            "parameters": [
              "7"
            ]
          }
        ]
      }
    },
    {
      "value": 32000,
      "rewardSet": {
        "rewards": [
          {
            "rewardType": "HeroStatsReward",
            "parameters": [
              "offence",
              "4",
              "defence",
              "4",
              "spellPower",
              "4",
              "intelligence",
              "4"
            ]
          }
        ]
      }
    }
  ]
}
  </script>

  <script>
    const units = JSON.parse(document.getElementById('unitsData').textContent).array;
    const boxes = JSON.parse(document.getElementById('boxesData').textContent).array;

    console.log('Загружено юнитов:', units.length);
    console.log('Загружено коробок:', boxes.length);
    console.log('Пример коробки:', boxes[0]);
    console.log('Пример юнита:', units[0]);

    const guard = [];

    // Инициализация фильтра фракций и списка юнитов
    const unitSelect = document.getElementById('unitSelect');
    const fractionFilter = document.getElementById('fractionFilter');

    // Сортируем юниты по названию для удобства
    const sortedUnits = units.sort((a, b) => a.id.localeCompare(b.id));

    // Получаем уникальные фракции
    const fractions = [...new Set(units.map(u => u.fraction))].sort();

    // Заполняем фильтр фракций
    fractions.forEach(fraction => {
      const opt = document.createElement('option');
      opt.value = fraction;
      opt.textContent = fraction.charAt(0).toUpperCase() + fraction.slice(1);
      fractionFilter.appendChild(opt);
    });

    // Функция фильтрации юнитов (глобальная)
    window.filterUnits = function() {
      const selectedFraction = document.getElementById('fractionFilter').value;
      const unitSelectElement = document.getElementById('unitSelect');
      unitSelectElement.innerHTML = '';

      let filteredUnits;
      if (selectedFraction === 'all') {
        filteredUnits = sortedUnits;
      } else {
        filteredUnits = sortedUnits.filter(u => u.fraction === selectedFraction);
      }

      console.log('Фильтрация:', selectedFraction, 'найдено юнитов:', filteredUnits.length);

      filteredUnits.forEach(u => {
        const opt = document.createElement('option');
        opt.value = u.id;
        opt.textContent = `${u.id} (ценность: ${u.squadValue})`;
        unitSelectElement.appendChild(opt);
      });
    };

    // Изначально показываем всех юнитов
    setTimeout(() => filterUnits(), 100);

    function addUnit() {
      const unitId = unitSelect.value;
      const selectedMode = document.querySelector('input[name="inputMode"]:checked').value;

      if (!unitId) {
        alert('Выберите юнит');
        return;
      }

      let count, countDisplay, isRange = false;

      if (selectedMode === 'exact') {
        count = parseInt(document.getElementById('countInput').value);
        if (isNaN(count) || count <= 0) {
          alert('Введите корректное количество');
          return;
        }
        countDisplay = count;
      } else {
        const rangeValue = document.getElementById('countRangeSelect').value;
        if (!rangeValue) {
          alert('Выберите диапазон количества');
          return;
        }
        count = rangeValue;
        countDisplay = rangeValue;
        isRange = true;
      }

      // Добавляем в охрану
      guard.push({
        id: unitId,
        count: count,
        countDisplay: countDisplay,
        isRange: isRange
      });

      renderGuard();

      // Сбрасываем поля
      if (selectedMode === 'exact') {
        document.getElementById('countInput').value = 1;
      } else {
        document.getElementById('countRangeSelect').selectedIndex = 0;
      }
    }

    function removeUnit(index) {
      guard.splice(index, 1);
      renderGuard();
    }

    function toggleInputMode() {
      const exactMode = document.getElementById('exactMode');
      const rangeMode = document.getElementById('rangeMode');
      const selectedMode = document.querySelector('input[name="inputMode"]:checked').value;

      // Очищаем текущую охрану при смене режима
      guard.length = 0;

      if (selectedMode === 'exact') {
        exactMode.style.display = 'inline-flex';
        rangeMode.style.display = 'none';
      } else {
        exactMode.style.display = 'none';
        rangeMode.style.display = 'inline-flex';
      }

      renderGuard();
      updateToleranceVisibility();
    }

    function updateToleranceVisibility() {
      const toleranceControl = document.getElementById('toleranceControl');
      const hasRanges = guard.some(g => g.isRange);

      // Скрываем допуск если в охране есть диапазоны
      if (hasRanges) {
        toleranceControl.style.display = 'none';
      } else {
        toleranceControl.style.display = 'block';
      }
    }



    function renderGuard() {
      const tbody = document.querySelector('#guardTable tbody');
      tbody.innerHTML = '';

      if (guard.length === 0) {
        const tr = document.createElement('tr');
        tr.innerHTML = '<td colspan="5" style="text-align: center; color: #666;">Охрана не добавлена</td>';
        tbody.appendChild(tr);
        return;
      }

      guard.forEach((g, i) => {
        const tr = document.createElement('tr');
        const unit = units.find(u => u.id === g.id);
        const unitValue = unit ? unit.squadValue : 0;

        if (g.isRange) {
          // Парсим диапазон количества
          let minCount, maxCount;
          if (g.count === '1000+') {
            minCount = 1000;
            maxCount = '∞';
          } else {
            const rangeParts = g.count.split('-');
            minCount = parseInt(rangeParts[0]);
            maxCount = parseInt(rangeParts[1]);
          }

          // Рассчитываем диапазон ценности
          const minValue = unitValue * minCount;
          const maxValue = maxCount === '∞' ? '∞' : unitValue * maxCount;
          const valueRange = maxValue === '∞' ? `${minValue}+` : `${minValue}–${maxValue}`;

          tr.innerHTML = `
            <td>${g.id}</td>
            <td>${unitValue}</td>
            <td>${g.countDisplay}</td>
            <td><strong>${valueRange}</strong></td>
            <td><button class="danger" onclick="removeUnit(${i})">Удалить</button></td>
          `;
        } else {
          // Отображение точного количества
          const totalValue = unitValue * g.count;

          tr.innerHTML = `
            <td>${g.id}</td>
            <td>${unitValue}</td>
            <td>${g.count}</td>
            <td><strong>${totalValue}</strong></td>
            <td><button class="danger" onclick="removeUnit(${i})">Удалить</button></td>
          `;
        }

        tbody.appendChild(tr);
      });

      updateToleranceVisibility();
    }

    function calculate() {
      if (guard.length === 0) {
        alert('Добавьте юниты в охрану перед расчетом');
        return;
      }

      const hasRanges = guard.some(g => g.isRange);
      let minVal, maxVal;

      if (hasRanges) {
        // Расчет для диапазонов
        let totalMin = 0, totalMax = 0;

        guard.forEach(g => {
          const unit = units.find(u => u.id === g.id);
          if (!unit) return;

          if (g.isRange) {
            // Парсим диапазон количества
            let minCount, maxCount;
            if (g.count === '1000+') {
              minCount = 1000;
              maxCount = 10000; // Используем большое число для расчета
            } else {
              const rangeParts = g.count.split('-');
              minCount = parseInt(rangeParts[0]);
              maxCount = parseInt(rangeParts[1]);
            }

            totalMin += unit.squadValue * minCount;
            totalMax += unit.squadValue * maxCount;
          } else {
            // Точное количество
            const value = unit.squadValue * g.count;
            totalMin += value;
            totalMax += value;
          }
        });

        minVal = totalMin;
        maxVal = totalMax;

        // Проверяем, есть ли открытый диапазон "1000+"
        const hasOpenRange = guard.some(g => g.isRange && g.count === '1000+');
        const maxDisplay = hasOpenRange ? `${totalMin}+` : totalMax;

        document.getElementById('totalValue').innerHTML = `
          <strong>Диапазон ценности охраны: ${totalMin}–${maxDisplay}</strong><br>
          <small>Поиск коробок в диапазоне: ${minVal} - ${maxVal}</small>
        `;
      } else {
        // Расчет для точных значений (как раньше)
        let total = 0;
        guard.forEach(g => {
          const unit = units.find(u => u.id === g.id);
          if (unit) total += g.count * unit.squadValue;
        });

        const tolerancePercent = parseInt(document.getElementById('toleranceInput').value) || 10;
        const tolerance = tolerancePercent / 100;

        minVal = total * (1 - tolerance);
        maxVal = total * (1 + tolerance);

        document.getElementById('totalValue').innerHTML = `
          <strong>Общая ценность охраны: ${total}</strong><br>
          <small>Диапазон поиска: ${Math.round(minVal)} - ${Math.round(maxVal)} (±${tolerancePercent}%)</small>
        `;
      }

      const tbody = document.querySelector('#resultTable tbody');
      tbody.innerHTML = '';

      const matchingBoxes = [];

      boxes.forEach(box => {
        if (box.value >= minVal && box.value <= maxVal) {
          box.rewardSet.rewards.forEach(r => {
            matchingBoxes.push({
              value: box.value,
              rewardType: r.rewardType,
              parameters: r.parameters
            });
          });
        }
      });

      if (matchingBoxes.length === 0) {
        const tr = document.createElement('tr');
        const message = hasRanges ?
          'Подходящие коробки не найдены в диапазоне ценности.' :
          'Подходящие коробки не найдены. Попробуйте увеличить допуск.';
        tr.innerHTML = `<td colspan="3" style="text-align: center; color: #666;">${message}</td>`;
        tbody.appendChild(tr);
        return;
      }

      // Сортируем по ценности
      matchingBoxes.sort((a, b) => a.value - b.value);

      matchingBoxes.forEach(box => {
        const tr = document.createElement('tr');
        const rewardText = formatRewardText(box.rewardType, box.parameters);
        tr.innerHTML = `
          <td><strong>${box.value}</strong></td>
          <td>${formatRewardType(box.rewardType)}</td>
          <td>${rewardText}</td>
        `;
        tbody.appendChild(tr);
      });
    }

    function formatRewardType(rewardType) {
      const types = {
        'SideResReward': 'Ресурсы',
        'HeroExpReward': 'Опыт героя',
        'HeroBoxUnitReward': 'Случайный юнит',
        'HeroStatsReward': 'Характеристики героя',
        'HeroMagicMassAdditionReward': 'Заклинания'
      };
      return types[rewardType] || rewardType;
    }

    function formatRewardText(rewardType, parameters) {
      switch (rewardType) {
        case 'SideResReward':
          return `${parameters[1]} ${parameters[0]}`;
        case 'HeroExpReward':
          return `${parameters[0]} опыта`;
        case 'HeroBoxUnitReward':
          return `${parameters[0]} случайный юнит`;
        case 'HeroStatsReward':
          const stats = [];
          for (let i = 0; i < parameters.length; i += 2) {
            stats.push(`${parameters[i]}: +${parameters[i + 1]}`);
          }
          return stats.join(', ');
        case 'HeroMagicMassAdditionReward':
          if (parameters[2] && parameters[2] !== 'any') {
            return `${parameters[2]} заклинание уровня ${parameters[2]}`;
          } else if (parameters[0] !== 'any') {
            return `Заклинание школы ${parameters[0]}`;
          } else {
            return 'Случайное заклинание';
          }
        default:
          return parameters.join(', ');
      }
    }

    // Инициализация при загрузке страницы
    document.addEventListener('DOMContentLoaded', function() {
      renderGuard();
      filterUnits(); // Дополнительный вызов для гарантии
    });
  </script>
</body>
</html>
