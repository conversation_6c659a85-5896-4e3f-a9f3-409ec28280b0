{"array": [{"id": "godslayer", "squadValue": 774, "expBonus": 77, "tier": 5, "fraction": "demon", "nativeBiome": "<PERSON><PERSON>", "ai": "melee_type", "tags": ["unit", "demon"], "upgradeSid": "godslayer_upg", "unitCost": {"costResArray": [{"name": "gold", "cost": 670}]}, "stats": {"hp": 45, "offence": 17, "defence": 13, "damageMin": 10, "damageMax": 40, "initiative": 8, "speed": 5, "luck": 0, "moral": 1, "energyPerCast": 2, "energyPerRound": 0, "energyPerTakeDamage": 1, "actionPoints": 1, "numCounters": 1, "moralMin": -5, "moralMax": 5, "luckMin": -5, "luckMax": 5, "outDamageIfLevelAbove": 1.0, "outLevelAboveThreshold ": 6, "inDmgMods": {"list": []}, "outDmgMods": {"list": []}}, "passives": [{"data": {"immunities": [{"type": "cast", "tags": ["demon_immunities"]}]}}], "defaultAttacks": [{"attackType_": "melee", "selfMechanics": [], "damageDealer": {"tags": ["melee_attack", "normal_damage", "basic_attack"], "triggerCounter": true, "attackPatternSid": "attack_single_x100", "castTargetParams": {"targetTags": ["unit", "attackable_object"], "castTarget_": "enemy", "targetCondition_": "alive", "selection": "object"}, "affectTargetParams": {"targetTags": ["unit", "attackable_object"], "castTarget_": "enemy", "targetCondition_": "alive", "selection": "object"}, "damageTarget_": "enemy", "damageType_": "normal", "statDmgMult": 1.0, "targetMechanics": []}}], "counterAttacks": [{"attackType_": "melee", "selfMechanics": [], "damageDealer": {"tags": ["melee_attack", "normal_damage", "basic_attack", "counter_attack"], "triggerCounter": false, "attackPatternSid": "attack_single_x100", "castTargetParams": {"targetTags": ["unit", "attackable_object"], "castTarget_": "all", "targetCondition_": "alive", "selection": "object"}, "affectTargetParams": {"targetTags": ["unit", "attackable_object"], "castTarget_": "all", "targetCondition_": "alive", "selection": "object"}, "damageTarget_": "enemy", "damageType_": "normal", "statDmgMult": 1.0, "targetMechanics": []}}], "abilities": [{"attackType_": "melee", "rank": 2, "selfMechanics": [], "cd": 1, "energyLevel": 1, "damageDealer": {"tags": ["melee_attack", "normal_damage", "basic_attack", "ability", "armed_ability"], "triggerCounter": false, "attackPatternSid": "attack_single_x100", "castTargetParams": {"targetTags": ["unit", "attackable_object"], "castTarget_": "enemy", "targetCondition_": "alive", "selection": "object"}, "affectTargetParams": {"targetTags": ["unit", "attackable_object"], "castTarget_": "enemy", "targetCondition_": "alive", "selection": "object"}, "damageTarget_": "enemy", "damageType_": "normal", "statDmgMult": 1.0, "targetMechanics": []}}]}]}