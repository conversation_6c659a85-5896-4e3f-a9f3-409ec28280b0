{"array": [{"id": "dragon", "squadValue": 5155, "expBonus": 515, "tier": 7, "fraction": "neutral", "tags": ["unit", "neutral", "dragon"], "upgradeSid": "dragon_upg", "unitCost": {"costResArray": [{"name": "gold", "cost": 3500}, {"name": "gemstones", "cost": 1}]}, "stats": {"hp": 250, "offence": 25, "defence": 26, "damageMin": 40, "damageMax": 50, "initiative": 6, "speed": 6, "luck": 0, "moral": 0, "energyPerCast": 2, "energyPerRound": 0, "energyPerTakeDamage": 1, "actionPoints": 1, "numCounters": 1, "moralMin": 0, "moralMax": 5, "luckMin": 0, "luckMax": 3, "inDmgMods": {"list": []}, "outDmgMods": {"list": []}, "moveType": "fly"}, "passives": [{"data": {"immunities": [{"type": "magic_level", "tags": ["3"]}, {"type": "ability_rank", "tags": ["3"]}, {"type": "cast", "tags": ["dragon_immunities"]}]}}, {"data": {"immunities": [{"type": "effect", "tags": ["overTime_effect_poison"]}, {"type": "effect", "tags": ["buff_effect_poison"]}]}}], "defaultAttacks": [{"attackType_": "melee", "selfMechanics": [], "damageDealer": {"tags": ["melee_attack", "normal_damage", "basic_attack"], "triggerCounter": true, "attackPatternSid": "attack_reach_x1_x100_x100_with_delay", "castTargetParams": {"targetTags": ["unit", "attackable_object"], "castTarget_": "enemy", "targetCondition_": "alive", "selection": "object"}, "affectTargetParams": {"targetTags": ["unit", "attackable_object"], "castTarget_": "all", "targetCondition_": "alive", "selection": "object"}, "damageTarget_": "all", "damageType_": "normal", "statDmgMult": 1.0, "targetMechanics": []}}], "counterAttacks": [{"attackType_": "melee", "selfMechanics": [], "damageDealer": {"tags": ["melee_attack", "normal_damage", "basic_attack", "counter_attack"], "triggerCounter": false, "attackPatternSid": "attack_reach_x1_x100_x100_with_delay", "castTargetParams": {"targetTags": ["unit", "attackable_object"], "castTarget_": "all", "targetCondition_": "alive", "selection": "object"}, "affectTargetParams": {"targetTags": ["unit", "attackable_object"], "castTarget_": "all", "targetCondition_": "alive", "selection": "object"}, "damageTarget_": "all", "damageType_": "normal", "statDmgMult": 1.0, "targetMechanics": []}}], "abilities": [{"attackType_": "cast", "rank": 5, "selfMechanics": [], "cd": -1, "charges": 1, "damageDealer": {"instacast": true, "multitargetType": "simultaneous", "tags": ["ability"], "attackPatternSid": "attack_single_buff", "castTargetParams": {"targetTags": ["unit"], "castTarget_": "all", "targetCondition_": "alive", "selection": "object"}, "affectTargetParams": {"targetTags": ["unit"], "castTarget_": "all", "targetCondition_": "alive", "selection": "object"}, "damageTarget_": "none", "targetMechanics": []}, "allyDealer": {"instacast": true, "multitargetType": "simultaneous", "tags": ["ability"], "attackPatternSid": "attack_single_buff", "castTargetParams": {"targetTags": ["unit"], "castTarget_": "all", "targetCondition_": "alive", "selection": "object"}, "affectTargetParams": {"targetTags": ["unit"], "castTarget_": "all", "targetCondition_": "alive", "selection": "object"}, "damageTarget_": "none", "targetMechanics": []}}]}]}