﻿{
	"array":
	[
		{
			"id": "sentinel",
			"squadValue": 7500,
			"expBonus": 750,
			"tier": 7,
			"fraction": "neutral",

			"ai": "melee_type",
			
			"tags": [ "unit", "neutral" ],

			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 5000 }
				]
			},

			"stats":
			{
				"hp": 400,
				"offence": 40,
				"defence": 40,
				"damageMin": 50,
				"damageMax": 50,

				"initiative": 8,
				"speed": 8,
				
				"luck": 0,
				"moral": 0,

				"energyPerCast": 2,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				"maxEnergy": 0,
				"startEnergy": 0,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": -3,
				"moralMax": 3,
				"luckMin": -5,
				"luckMax": 5,

				"inDmgMods": 
				{
					"list": 
					[ 

					]
				},

				"outDmgMods": 
				{
					"list": 
					[ 

					]
				},

				"moveType": "fly"	
			},
			
			"passives": 
			[
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "cast", "tags": [ "magic_creature_immunities" ] }
						]
					}
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "effect", "tags": [ "overTime_effect_curse" ] }
						]
					}
				}
			],
			
			"defaultAttacks": 
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"triggerCounter" : true,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				} 
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,
						
						"targetMechanics" : [ ]
					}
				}
			],
				
			"abilities" : 
			[ 
				{
					"attackType_" : "cast",
					"rank": 2,
					
					"selfMechanics" : 
					[ 
						{
							"mech": "dispel", "values":  [ "negative" ]
						},

						{
							"mech": "heal_percent", "values":  [ "without respawn",  "max_unit",  "1.0",  "0.00",  "true" ]
						},

						{ 
							"mech": "force_unit_cd", "values":  [ "0" ] 
						}
					],

					"cd" : 3,
					"charges" : 1,
					"actionCost": 0,

					"energyLevel": 1,

					"damageDealer" : 
					{
						"instacast": true,
						"multitargetType": "simultaneous",
						
						"tags": [ "ability", "charge_ability", "heal_ability", "dispel_ability" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_buff",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "self",
							"targetCondition_": "alive"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "self",
							"targetCondition_": "alive"
						},

						"damageTarget_": "none",

						"buffTarget_": "none",

						"targetMechanics" : [ ]
					}
				},
				{
					"attackType_" : "cast",
					"rank": 2,
					
					"selfMechanics" : [ { "mech": "force_unit_cd", "values":  [ "0" ] }	],

					"cd" : 3,
					"charges" : 1,
					"actionCost": 0,

					"energyLevel": 1,

					"damageDealer" : 
					{
						"instacast": true,
						"multitargetType": "simultaneous",
						
						"tags": [ "ability", "charge_ability", "buff_ability" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_buff",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "self",
							"targetCondition_": "alive"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "self",
							"targetCondition_": "alive"
						},

						"damageTarget_": "none",

						"buffTarget_": "self",
						"buff":
						{
							"sid": "sentinel_buff_1",
							"duration": 1
						},

						"targetMechanics" : [ ]
					}
				},
				{
					"attackType_" : "end_move_attack",
					"moveType": "teleport",
					"rank": 2,
					
					"selfMechanics" : 
					[ 
						{ 
							"mech": "force_unit_cd", "values":  [ "0" ] 
						} 
					],
					
					"cd" : 3,
					"charges" : 1,
					"actionCost": 0,

					"energyLevel": 1,
					
					"damageDealer" : 
					{
						"tags": [ "ability", "charge_ability", "movement_ability", "absolute_damage" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_swirl_x1_x100",
						"shootRange": -1,
						"castTargetParams":   
						{
							"targetTags": [],
							"castTarget_": "none",
							"selection": "hex"
						},

						"affectTargetParams":   
						{
							"targetTags": [ "unit" ],
							"castTarget_": "all",
							"targetCondition_": "alive"
						},

						"damageType_": "absolute",

						"buffTarget_": "none",

						"targetMechanics" : [ ]
					}
				},
				{
					"attackType_" : "cast",
					"rank": 2,
					
					"selfMechanics" : 
					[ 
						{ 
							"mech": "force_unit_cd", "values":  [ "0" ]
						}
					],

					"cd" : 3,
					"charges" : 1,
					"actionCost": 0,

					"energyLevel": 1,

					"damageDealer" : 
					{
						"instacast": true,
						"multitargetType": "simultaneous",
						
						"tags": [ "ability", "charge_ability", "buff_ability" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_buff",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "self",
							"targetCondition_": "alive"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "self",
							"targetCondition_": "alive"
						},

						"damageTarget_": "none",

						"buffTarget_": "self",

						"buff":
						{
							"sid": "sentinel_buff_2",
							"duration": 1
						},

						"targetMechanics" : [ ]
					}
				}
			]
		}
	]
}