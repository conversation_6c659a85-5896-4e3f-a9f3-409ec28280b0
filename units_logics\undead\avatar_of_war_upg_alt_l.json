﻿{
	"array":
	[
		{
			"id": "avatar_of_war_upg_alt",
			"squadValue": 1316,
			"expBonus": 131,
			"tier": 6,
			"fraction": "undead",
			"nativeBiome": "Deathland",

			"ai": "melee_type",
			
			"tags": [ "unit", "undead" ],

			"baseSid": "avatar_of_war_upg",

			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 1000 }
				]
			},
			
			"stats":
			{
				"hp": 90,
				"offence": 22,
				"defence": 18,
				"damageMin": 22,
				"damageMax": 24,

				"initiative": 7,
				"speed": 6,
				
				"luck": 0,
				"moral": 0,

				"energyPerCast": 2,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": 0,
				"moralMax": 0,
				"luckMin": -5,
				"luckMax": 5,

				"inDmgMods":
				{
					"list":
					[				
						
					]
				},

				"outDmgMods":
				{
					"list":
					[
									
					]
				}
			},
			
			"passives" : 
			[
				{
					"actions": 
					[
						{
							"trigger": "unit_died",
							"triggerConditions": 
							[
								{
									"checkFunction": "allegiance",
									"values": [ "ally" ]
								}
							],
							"damageDealer": 
							{
								"attackPatternSid": "attack_single_x100",

								"castTargetParams":   
								{ 
									"targetTags": [ "unit" ],
									"castTarget_": "ally",
									"targetCondition_": "alive"
								},

								"affectTargetParams":   
								{ 
									"targetTags": [ "unit" ],
									"castTarget_": "ally",
									"targetCondition_": "alive"
								},
								
								"buffTarget_": "ally",
								"buff": 
								{
									"sid": "avatar_of_war_additionbuff",
									"duration": 1
								}
							}
						}
					]
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "cast", "tags": [ "undead_immunities" ] }
						]
					}
				},
				{
					"data": 
					{
						"disablers": 
						[ 
							{ "mech": "vampirism" },
							{ "mech": "destroy_corpse_and_heal" }
						]
					}
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "effect", "tags": [ "overTime_effect_curse" ] },
							{ "type": "effect", "tags": [ "overTime_effect_blood" ] },
							{ "type": "effect", "tags": [ "overTime_effect_poison" ] },
							{ "type": "effect", "tags": [ "buff_effect_poison" ] }
						]
					}
				}
			],
			
			"globalPassives" : 
			[
				{
					"tag": "avatar_of_war_aura",
					"target": "enemy",
					"power": 2,
					"data": 
					{
						"stats" :
						{
							"moral" : -2
						}
					}	
				}
			],
			
			"defaultAttacks": 
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"triggerCounter": true,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"buffTarget_": "enemy",
						"buff":
						{
							"sid": "avatar_of_war_debuff",
							"duration": -1
						},

						"targetMechanics" : [ { "mech": "kill_stacks", "values": [ "1", "7" ] } ]
					}
				} 
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"buffTarget_": "enemy",
						"buff":
						{
							"sid": "avatar_of_war_debuff",
							"duration": -1
						},

						"targetMechanics" : [ { "mech": "kill_stacks", "values": [ "1", "7" ] } ]
					}
				}
			],
				
			"abilities" : 
			[ 
				{
					"attackType_" : "melee",

					"rank": 3,
					
					"selfMechanics" : 
					[

					],

					"cd" : 2,
					"energyLevel": 2,

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "ability", "armed_ability", "heal_ability", "revive_ability" ],
						"triggerCounter": true,
						"attackPatternSid" : "attack_swipe_x100_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"targetMechanics" : [ { "mech": "kill_stacks", "values": [ "1", "7" ] } ]
					}
				}
			]
		}
	]
}