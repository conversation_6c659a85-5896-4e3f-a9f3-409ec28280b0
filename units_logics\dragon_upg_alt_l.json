﻿{
	"array":
	[
		{
			"id": "dragon_upg_alt",
			"squadValue": 8985,
			"expBonus": 898,
			"tier": 7,
			"fraction": "neutral",

			"tags": [ "unit", "neutral", "dragon" ],

			"baseSid": "dragon_upg",

			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 5800 },
					{ "name": "gemstones", "cost": 2  }
				]
			},
			
			"stats":
			{
				"hp": 300,
				"offence": 28,
				"defence": 31,
				"damageMin": 50,
				"damageMax": 60,

				"initiative": 10,
				"speed": 7,
				
				"luck": 0,
				"moral": 0,

				"energyPerCast": 2,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": 0,
				"moralMax": 5,
				"luckMin": 0,
				"luckMax": 3,

				"inDmgMods": 
				{
					"list": 
					[ 

					]
				},

				"outDmgMods": 
				{
					"list": 
					[ 

					]
				},

				"moveType":"fly"	
			},

			"passives": 
			[
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "magic_level", "tags": [ "4" ] },
							
							{ "type": "ability_rank", "tags": [ "4" ] },
							
							{ "type": "cast", "tags": [ "dragon_immunities" ] }
						]
					}
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "effect", "tags": [ "overTime_effect_poison" ] },
							{ "type": "effect", "tags": [ "buff_effect_poison" ] }
						]
					}
				}
			],
			
			"defaultAttacks": 
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"triggerCounter" : true,
						"attackPatternSid" : "attack_reach_x1_x100_x100_with_delay",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "all",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				} 
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_reach_x1_x100_x100_with_delay",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "all",
						"damageType_": "normal",

						"statDmgMult": 1.0,
						
						"targetMechanics" : [ ]
					}
				}
			],

			"abilities": 
			[
				{
					"attackType_" : "cast",
					"rank": 5,
					
					"selfMechanics" : [ ],

					"cd" : -1,
					"charges" : 1,

					"damageDealer" : 
					{
						"instacast": true,
						"multitargetType": "simultaneous",
						
						"tags": [ "ability" ],
						"attackPatternSid" : "attack_single_buff",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "none",

						"targetMechanics" : [ ]
					},
					"allyDealer" : 
					{
						"instacast": true,
						"multitargetType": "simultaneous",
						
						"tags": [ "ability" ],
						"attackPatternSid" : "attack_single_buff",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "none",

						"targetMechanics" : [ ]
					}
				}
			]
		}
	]
}