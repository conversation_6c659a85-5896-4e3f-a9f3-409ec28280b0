#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Скрипт для извлечения данных Pandora Box из JSON файла
Извлекает value, rewardType, parameters из pandora_box.json
"""

import json
import os

def extract_pandora_data(json_file_path):
    """
    Извлекает данные коробок Pandora из JSON файла
    
    Args:
        json_file_path (str): Путь к JSON файлу
        
    Returns:
        list: Список словарей с данными коробок
    """
    try:
        # Пробуем разные кодировки для обработки BOM
        encodings = ['utf-8-sig', 'utf-8', 'utf-16', 'cp1251']
        data = None
        
        for encoding in encodings:
            try:
                with open(json_file_path, 'r', encoding=encoding) as file:
                    data = json.load(file)
                break
            except (UnicodeDecodeError, json.JSONDecodeError):
                continue
        
        if data is None:
            raise Exception("Не удалось прочитать файл с поддерживаемыми кодировками")
            
        boxes = []
        
        # Проверяем структуру файла
        if 'array' in data and isinstance(data['array'], list):
            for box in data['array']:
                if isinstance(box, dict) and 'variants' in box:
                    # Обрабатываем каждый вариант коробки
                    for variant in box['variants']:
                        if isinstance(variant, dict) and 'value' in variant and 'rewardSet' in variant:
                            value = variant['value']
                            reward_set = variant['rewardSet']
                            
                            # Проверяем наличие наград
                            if 'rewards' in reward_set and isinstance(reward_set['rewards'], list):
                                for reward in reward_set['rewards']:
                                    if isinstance(reward, dict) and 'rewardType' in reward and 'parameters' in reward:
                                        box_data = {
                                            'value': value,
                                            'rewardSet': {
                                                'rewards': [{
                                                    'rewardType': reward['rewardType'],
                                                    'parameters': reward['parameters']
                                                }]
                                            }
                                        }
                                        boxes.append(box_data)
        
        return boxes
        
    except (json.JSONDecodeError, FileNotFoundError, UnicodeDecodeError) as e:
        print(f"Ошибка при обработке файла {json_file_path}: {e}")
        return []

def main():
    """
    Основная функция скрипта
    """
    input_file = 'pandora_box.json'
    output_file = 'pandora.txt'
    
    print(f"Обработка файла: {input_file}")
    
    # Проверяем существование файла
    if not os.path.exists(input_file):
        print(f"Ошибка: Файл {input_file} не найден!")
        return
    
    # Извлекаем данные коробок
    boxes = extract_pandora_data(input_file)
    
    if not boxes:
        print("Данные коробок не найдены или ошибка при обработке")
        return
    
    print(f"Извлечено {len(boxes)} вариантов коробок")
    
    # Сортируем по ценности для удобства
    boxes.sort(key=lambda x: x['value'])
    
    # Сохраняем в файл
    try:
        with open(output_file, 'w', encoding='utf-8') as file:
            # Записываем в красивом формате
            file.write('{\n  "array": [\n')
            
            for i, box in enumerate(boxes):
                # Формируем строку для коробки
                reward = box['rewardSet']['rewards'][0]  # Берем первую (и единственную) награду
                
                box_str = '    { '
                box_str += f'"value": {box["value"]}, '
                box_str += f'"rewardSet": {{ "rewards": [{{ '
                box_str += f'"rewardType": "{reward["rewardType"]}", '
                box_str += f'"parameters": {json.dumps(reward["parameters"])} '
                box_str += '}}] } }'
                
                # Добавляем запятую если не последний элемент
                if i < len(boxes) - 1:
                    box_str += ','
                
                file.write(box_str + '\n')
            
            file.write('  ]\n}')
        
        print(f"\nДанные успешно сохранены в файл: {output_file}")
        
        # Выводим статистику
        reward_types = {}
        values = []
        
        for box in boxes:
            reward_type = box['rewardSet']['rewards'][0]['rewardType']
            reward_types[reward_type] = reward_types.get(reward_type, 0) + 1
            values.append(box['value'])
        
        print("\nСтатистика по типам наград:")
        for reward_type, count in sorted(reward_types.items()):
            print(f"  {reward_type}: {count} коробок")
        
        print(f"\nДиапазон ценностей:")
        print(f"  Минимальная: {min(values)}")
        print(f"  Максимальная: {max(values)}")
        print(f"  Уникальных значений: {len(set(values))}")
        
        # Показываем примеры
        print(f"\nПримеры коробок:")
        for i, box in enumerate(boxes[:5]):
            reward = box['rewardSet']['rewards'][0]
            params_str = ', '.join(map(str, reward['parameters']))
            print(f"  Ценность {box['value']}: {reward['rewardType']} ({params_str})")
        
        if len(boxes) > 5:
            print(f"  ... и еще {len(boxes) - 5} коробок")
            
    except Exception as e:
        print(f"Ошибка при сохранении файла: {e}")

if __name__ == "__main__":
    main()
