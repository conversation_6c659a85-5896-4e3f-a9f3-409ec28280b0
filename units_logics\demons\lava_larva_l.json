﻿{
	"array":
	[
		{
			"id": "lava_larva",
			"squadValue": 100,
			"expBonus": 0,
			"tier": 4,
			"fraction": "demon",
			"nativeBiome": "Lava",

			"ai": "melee_type",
			
			"tags": [ "unit", "demon" ],
			
			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 100 }
				]
			},

			"leaveCorpse": false,

			"stats":
			{	
				"hp": 8,
				"offence": 4,
				"defence": 4,
				"damageMin": 4,
				"damageMax": 4,

				"initiative": 4,
				"speed": 4,
				
				"luck": 0,
				"moral": 0,

				"energyPerCast": 1,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": -5,
				"moralMax": 5,
				"luckMin": -5,
				"luckMax": 5,

				"inDmgMods":
				{
					"list":
					[

					]
				},

				"outDmgMods":
				{
					"list":
					[
									
					]
				}
			},
			
			"passives": 
			[
				{
					"actions":
					[
						{
							"trigger": "unit_died",
							"triggerConditions": 
							[
								{
									"checkFunction": "allegiance",
									"values": [ "self" ]
								}
							],

							"damageDealer": 
							{
								"tags": [ "passive", "magic_damage", "lava_larva_damage", "lava_larva_explosion_attack" ],
								"triggerCounter": false,

								"castTargetParams":   
								{ 
									"targetTags": [ "unit" ],
									"castTarget_": "self"
								},

								"affectTargetParams":   
								{ 
									"targetTags": [ "unit" ],
									"castTarget_": "all"
								},

								"attackPatternSid": "attack_rumble_x1_x100",	"damageTarget_": "noself",	"damageType_": "magic_pure",

								"statDmgMult": 0.0,
								"minStackDmg": 4,
								"maxStackDmg": 4,

								"buffTarget_":"none", 

								"targetMechanics": [ ]
							}
						},
						{
							"trigger": "start_turn",
							"triggerConditions": 
							[
								{
									"checkFunction": "allegiance",
									"values": [ "self" ]
								}
							],

							"damageDealer": 
							{
								"tags": [ "passive" ],
								"triggerCounter": false,

								"damageTarget_": "none",
								"statDmgMult": 0.0,

								"attackPatternSid": "attack_single_x100",

								"castTargetParams":   
								{ 
									"targetTags": [ "unit" ],
									"castTarget_": "all",
									"selection": "object"
								},

								"affectTargetParams":   
								{ 
									"targetTags": [ "unit" ],
									"castTarget_": "all",
									"selection": "object"
								},

								"buffTarget_": "ally",
								"buff":
								{
									"sid": "self_destruct_buff",
									"duration": 2
								},
								
								"targetMechanics" : [ ]
							}
						}
					]
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "cast", "tags": [ "demon_immunities" ] }
						]
					}
				}
			],
			
			"defaultAttacks": 
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"triggerCounter" : true,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				} 
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,
						
						"targetMechanics" : [ ]
					}
				}
			],
				
			"abilities" : 
			[ 
				{
					"attackType_" : "cast",
					"rank": 1,
					
					"selfMechanics" : [ { "mech": "destroy_self", "values": [ ], "afterAttack": true  } ],

					"cd" : 0,
					"energyLevel": 1,

					"damageDealer" : 
					{
						"instacast": true,
						"multitargetType": "simultaneous",

						"tags": [ "ability", "magic_damage", "lava_larva_damage", "lava_larva_explosion_attack" ],
						"triggerCounter": false,

						"castTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "self",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"attackPatternSid": "attack_rumble_x1_x100",	"damageTarget_": "noself",	"damageType_": "magic_pure",

						"statDmgMult": 0.0,
						"minStackDmg": 4,
						"maxStackDmg": 4
					}
				},
				{
					"attackType_" : "jump",
					"rank": 1,
					
					"selfMechanics" : [ { "mech": "destroy_self", "values": [ ], "afterAttack": true  } ],
																				
					"cd" : 0,
					"energyLevel": 1,
					
					"damageDealer" : 
					{	
						"shootRange": 2,

						"tags": [ "ability", "movement_ability", "magic_damage", "lava_larva_damage", "lava_larva_explosion_attack" ],
						"triggerCounter": false,

						"castTargetParams":   
						{
							"targetTags": [ "unit" ],
							"castTarget_": "all",
							"selection": "hexOrObject",
							"targetCondition_": "alive"
						},

						"affectTargetParams":   
						{
							"targetTags": [ "unit" ],
							"castTarget_": "all",
							"selection": "object", 
							"targetCondition_": "alive" 
						},

						"attackPatternSid": "attack_rumble_x1_x100",	"damageTarget_": "noself",	"damageType_": "magic_pure",

						"statDmgMult": 0.0,
						"minStackDmg": 4,
						"maxStackDmg": 4
					}
				}
			]
		}
	]
}