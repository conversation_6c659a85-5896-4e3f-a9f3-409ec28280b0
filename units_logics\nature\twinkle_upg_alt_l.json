﻿{
	"array":
	[
		{
			"id": "twinkle_upg_alt",
			"squadValue": 163,
			"expBonus": 16,
			"tier": 2,
			"fraction": "nature",
			"nativeBiome": "Autumn",

			"ai": "melee_type",
			
			"tags": [ "unit", "nature" ],

			"baseSid": "twinkle_upg",

			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 170 }
				]
			},
			
			"stats":
			{
				"hp": 15,
				"offence": 5,
				"defence": 5,
				"damageMin": 3,
				"damageMax": 6,

				"initiative": 7,
				"speed": 4,
				
				"luck": 2,
				"moral": 3,

				"energyPerCast": 2,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": -3,
				"moralMax": 3,
				"luckMin": -5,
				"luckMax": 5,

				"inDmgMods": 
				{
					"list": 
					[ 

					]
				},

				"outDmgMods": 
				{
					"list": 
					[ 

					]
				},
				
				"moveType":"fly"
			},
			
			"passives": 
			[
				{
					"actions": 
					[
						{
							"trigger": "unit_died",
							"triggerConditions": 
							[
								{
									"checkFunction": "allegiance",
									"values": [ "self" ]
								}
							],
							"damageDealer": 
							{
								"attackPatternSid": "attack_single_x100",

								"castTargetParams":   
								{ 
									"targetTags": [ "unit" ],
									"castTarget_": "all",
									"selection": "hexOrObject",
									"targetCondition_": "all"
								},

								"affectTargetParams":   
								{ 
									"targetTags": [ "unit" ],
									"castTarget_": "all",
									"selection": "hexOrObject",
									"targetCondition_": "all"
								},

								"targetMechanics" : [ { "mech": "side_buff", "values": [ "side_buff_unit_twinkle_death" ] } ]
							}
						}
					]
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "cast", "tags": [ "magic_creature_immunities" ] }
						]
					}
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "effect", "tags": [ "overTime_effect_curse" ] }
						]
					}
				}
			],
			
			"defaultAttacks": 
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"triggerCounter" : true,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				} 
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,
						
						"targetMechanics" : [ ]
					}
				}
			],

			"abilities": 
			[ 
				{
					"disableForAi": true,
					"attackType_" : "cast",
					"rank": 3,
					
					"selfMechanics" : 
					[ 
						{
							"mech": "change_mana", "values":  [ "transfer" ,  "ally" ,  "1" ,  "5" , "1" ]	
						}
					],
										
					"cd" : 2,
					"energyLevel": 2,

					"damageDealer" : 
					{
						"instacast": true,
						"multitargetType": "simultaneous",
						
						"tags": [ "ability" ],
						"attackPatternSid" : "attack_single_buff",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "self",
							"targetCondition_": "alive"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "self",
							"targetCondition_": "alive"
						},

						"damageTarget_": "none",

						"targetMechanics" : [ ]
					}
				},
				{
					"disableForAi": true,
					"attackType_" : "cast",
					"rank": 3,
					
					"selfMechanics" : [ ],
																				
					"cd" : 1,
					"energyLevel": 1,
					
					"damageDealer" : 
					{
						"instacast": true,
						"multitargetType": "simultaneous",
						
						"tags": [ "ability", "buff_ability" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_buff",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "self",
							"targetCondition_": "alive"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "self",
							"targetCondition_": "alive"
						},

						"damageTarget_": "none",
						
						"targetMechanics" : 
						[ 
							{ "mech": "side_buff", "values": [ "side_buff_unit_twinkle" ] }
						]
					}
				}
			]
		}
	]
}