﻿{
	"array":
	[
		{
			"id": "obsidian_dragon",
			"squadValue": 7500,
			"expBonus": 750,
			"tier": 7,
			"fraction": "neutral",

			"ai": "melee_type",
			
			"tags": [ "unit", "neutral", "dragon" ],

			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 5000 }
				]
			},
			
			"stats":
			{
				"hp": 350,
				"offence": 38,
				"defence": 26,
				"damageMin": 60,
				"damageMax": 70,

				"initiative": 6,
				"speed": 9,
				
				"luck": 0,
				"moral": 0,

				"energyPerCast": 2,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": 0,
				"moralMax": 5,
				"luckMin": 0,
				"luckMax": 3,

				"inDmgMods": 
				{
					"list":
					[
						{ "t": "melee_attack", "v": -0.25 },
						{ "t": "range_attack", "v": -0.50 },
						{ "t": "shoot_attack", "v": -0.75 }
					]
				},

				"outDmgMods": 
				{
					"list": 
					[ 

					]
				},

				"moveType":"fly"
			},

			"passives": 
			[
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "magic_level", "tags": [ "4" ] },
							
							{ "type": "ability_rank", "tags": [ "4" ] },
							
							{ "type": "cast", "tags": [ "dragon_immunities" ] }
						]
					}
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "effect", "tags": [ "overTime_effect_poison" ] },
							{ "type": "effect", "tags": [ "buff_effect_poison" ] }
						]
					}
				}
			],
			
			"defaultAttacks": 
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"triggerCounter" : false,
						"attackPatternSid" : "attack_reach_x1_x100_x100_with_delay",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "all",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				} 
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_reach_x1_x100_x100_with_delay",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "all",
						"damageType_": "normal",

						"statDmgMult": 1.0,
						
						"targetMechanics" : [ ]
					}
				}
			],

			"abilities": [ ]
		}
	]
}