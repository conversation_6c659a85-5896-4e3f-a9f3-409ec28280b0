﻿{
	"array":
	[
		{
			"id": "unspeakable",
			"squadValue": 4124,
			"expBonus": 412,
			"tier": 7,
			"fraction": "unfrozen",
			"nativeBiome": "Snow",

			"ai": "melee_type",
			
			"tags": [ "unit", "unfrozen" ],

			"upgradeSid": "unspeakable_upg",
			
			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 2500 },
					{ "name": "mercury", "cost": 1  }
				]
			},

			"stats":
			{	
				"hp": 250,
				"offence": 30,
				"defence": 30,
				"damageMin": 35,
				"damageMax": 70,

				"initiative": 6,
				"speed": 4,
				
				"luck": 0,
				"moral": 0,

				"energyPerCast": 2,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": -3,
				"moralMax": 3,
				"luckMin": -5,
				"luckMax": 5,

				"inDmgMods": 
				{
					"list": 
					[ 

					]
				},

				"outDmgMods": 
				{
					"list": 
					[ 

					]
				}
			},
			
			"passives" : 
			[
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "damage", "tags": [ "magic_damage" ] }
						]
					}
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "cast", "tags": [ "magic_creature_immunities" ] }
						]
					}
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "effect", "tags": [ "overTime_effect_curse" ] }
						]
					}
				}
			],

			"defaultAttacks": 
			[
				{
					"attackType_" : "melee",

					"selfMechanics" : [ ],

					"damageDealer": 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"attackPatternSid": "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics": [ ]
					}
				}
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				}
			],

			"abilities" : 
			[
				{
					"attackType_" : "cast",
					"rank": 5,
					
					"selfMechanics" : [ ],
																									
					"cd" : 2,
					"charges" : 1,
					"energyLevel": 2,

					"damageDealer" : 
					{
						"tags": [ "ability", "revive_ability", "undead_immunities", "embodiment_immunities", "construct_immunities" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_buff",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "allynoself", 
							"selection": "object", 
							"targetCondition_": "dead" 
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "allynoself", 
							"selection": "object", 
							"targetCondition_": "dead" 
						},

						"damageTarget_": "none",

						"targetMechanics" : 
						[ 
							{ "mech": "substitute_by_hp", "values":  [ "unspeakable" ,  "0.5" ,  "true",  "true" ] } 
						]
					}
				},
				{
					"attackType_" : "cast",
					"rank": 5,
					
					"selfMechanics" : [ ],

					"cd" : 3,
					"actionCost": 0,
					"energyLevel": 3,

					"damageDealer" : 
					{
						"instacast": true,
						"multitargetType": "simultaneous",
						
						"tags": [ "ability", "buff_ability" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_buff",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "self",
							"targetCondition_": "alive"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "self",
							"targetCondition_": "alive"
						},

						"damageTarget_": "none",

						"buffTarget_": "self",
						"buff": 
						{
							"sid": "unspeakable_buff",
							"duration": 1
						},
						
						"targetMechanics" : 
						[ 
							{ "mech": "add_action_points", "values": [ "1" ] }
						]
					}
				}
			]
		}
	]
}