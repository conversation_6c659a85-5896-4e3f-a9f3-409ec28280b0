﻿{
	"array":
	[
		{
			"id": "lich_dragon",
			"squadValue": 30000,
			"expBonus": 3000,
			"tier": 8,
			"fraction": "neutral",

			"ai": "melee_type",
			
			"tags": [ "unit", "neutral" ],

			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 30000 }
				]
			},

			"stats":
			{	
				"hp": 999,
				"offence": 66,
				"defence": 66,
				"damageMin": 99,
				"damageMax": 99,

				"initiative": 9,
				"speed": 9,
				
				"luck": 0,
				"moral": 0,

				"energyPerCast": 2,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": 0,
				"moralMax": 0,
				"luckMin": -5,
				"luckMax": 5,

				"inDmgMods": 
				{
					"list": 
					[ 
						{ "t": "magic_damage", "v": -0.6 }
					]
				},

				"outDmgMods": 
				{
					"list": 
					[ 

					]
				},

				"moveType":"fly"
			},

			"passives": 
			[
				{
					"actions": [
						{
							"trigger": "unit_end_move",
							"triggerConditions": 
							[
								{
									"checkFunction": "allegiance",
									"values": [ "self" ]
								}
							],

							"damageDealer": 
							{
								"tags": [ "passive" ],
								"triggerCounter": false,

								"damageTarget_": "none",
								"statDmgMult": 0.0,

								"attackPatternSid": "attack_single_x100",

								"castTargetParams":
								{ 
									"targetTags": [ "unit" ],
									"castTarget_": "ally"
								},

								"affectTargetParams":
								{ 
									"targetTags": [ "unit" ],
									"castTarget_": "ally"
								},

								"targetMechanics":
								[
									{ "mech": "destroy_corpse_with_buff", "values": [ "1", "lich_dragon_corpse_buff" ] }
								]
							}
						}
					]
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "cast", "tags": [ "undead_immunities" ] }
						]
					}
				},
				{
					"data": 
					{
						"disablers": 
						[ 
							{ "mech": "vampirism" },
							{ "mech": "destroy_corpse_and_heal" }
						]
					}
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "effect", "tags": [ "overTime_effect_curse" ] },
							{ "type": "effect", "tags": [ "overTime_effect_blood" ] },
							{ "type": "effect", "tags": [ "overTime_effect_poison" ] },
							{ "type": "effect", "tags": [ "buff_effect_poison" ] }
						]
					}
				}
			],
			
			"defaultAttacks": 
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"triggerCounter" : true,
						"attackPatternSid" : "attack_swipe_x100_x100_x2",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "all",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				} 
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_swipe_x100_x100_x2",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "all",
						"damageType_": "normal",

						"statDmgMult": 1.0,
						
						"targetMechanics" : [ ]
					}
				}
			],
					
			"abilities" : 
			[
				{
					"attackType_" : "cast",
					"rank": 5,
					
					"selfMechanics" : [ ],
															
					"cd" : 3,
					"energyLevel": 3,
					
					"damageDealer" : 
					{
						"instacast": true,
						"multitargetType": "simultaneous",
						
						"tags": [ "magic_damage", "absolute_damage", "ability" ],
						"triggerCounter": false,

						"castTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "noself"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "noself"
						},

						"attackPatternSid": "attack_single_x100",	"damageTarget_": "noself",	"damageType_": "absolute",
						"statDmgMult": 0.0,
						"minBaseDmg": 0,
						"maxBaseDmg": 0,
						"minStackDmg": 333,
						"maxStackDmg": 333,

						"targetMechanics" : [ ]
					}
				},
				{
					"attackType_" : "cast",
					"rank": 5,
					
					"selfMechanics" : 
					[ 
						{
							"mech": "heal_percent",	"values":	[ "with temp stacks",  "max_unit",  "1.0",  "0.00",  "false" ]
						}
					],
															
					"cd" : 3,
					"energyLevel": 3,

					"damageDealer" : 
					{
						"instacast": true,
						"multitargetType": "simultaneous",
						
						"tags": [ "ability", "heal_ability", "revive_ability" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_buff",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "self",
							"targetCondition_": "alive"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "self",
							"targetCondition_": "alive"
						},

						"damageTarget_": "none",

						"targetMechanics" : [ ]
					}
				}
			]
		}
	]
}