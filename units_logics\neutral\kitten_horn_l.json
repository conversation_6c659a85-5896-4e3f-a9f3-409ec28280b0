﻿{
	"array":
	[
		{
			"id": "kitten_horn",
			"squadValue": 550,
			"expBonus": 55,
			"tier": 4,
			"fraction": "neutral",

			"ai": "melee_type",
			
			"tags": [ "unit", "neutral" ],

			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 550 }
				]
			},
			
			"stats":
			{
				"hp": 40,
				"offence": 10,
				"defence": 10,
				"damageMin": 10,
				"damageMax": 15,

				"initiative": 5,
				"speed": 5,
				
				"luck": 3,
				"moral": 3,

				"energyPerCast": 2,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": -5,
				"moralMax": 5,
				"luckMin": -3,
				"luckMax": 3,

				"inDmgMods": 
				{
					"list": 
					[ 

					]
				},

				"outDmgMods": 
				{
					"list": 
					[ 

					]
				},

				"moveType":"fly"
			},

			"passives": 
			[
				{
					"actions":
					[
						{
							"trigger": "unit_died",
							"triggerConditions": 
							[
								{
									"checkFunction": "allegiance",
									"values": [ "self" ]
								}
							],

							"damageDealer": 
							{
								"tags": [ "passive" ],
								"triggerCounter": false,

								"damageTarget_": "none",
								"statDmgMult": 0.0,

								"attackPatternSid": "attack_single_x100",

								"castTargetParams":   
								{
									"targetTags": [ "unit" ],
									"castTarget_": "ally",
									"targetCondition_": "all"
								},

								"affectTargetParams":   
								{
									"targetTags": [ "unit" ],
									"castTarget_": "ally",
									"targetCondition_": "all"
								},

								"targetMechanics":
								[
									{ "mech": "revenge_berserk", "values": [ "8" ] }
								]
							}
						}
					]
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "cast", "tags": [ "magic_creature_immunities" ] }
						]
					}
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "effect", "tags": [ "overTime_effect_curse" ] }
						]
					}
				}
			],
			
			"defaultAttacks": 
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"triggerCounter" : true,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				} 
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,
						
						"targetMechanics" : [ ]
					}
				}
			],
				
			"abilities" : 
			[ 
				{
					"attackType_" : "cast",
					"rank": 4,
					
					"selfMechanics" : [ ],
															
					"cd" : 2,
					"energyLevel": 2,
					
					"damageDealer" : 
					{
						"instacast": true,
						"multitargetType": "simultaneous",
						
						"tags": [ "ability", "buff_ability" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_buff",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "ally"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "ally"
						},

						"damageTarget_": "none",

						"buffTarget_": "allynoself",
						"buff":
						{
							"sid": "kitten_horn_buff",
							"duration": 2
						},
						
						"targetMechanics" : [ ]
					}
				}
			]
		}
	]
}