﻿{
	"array":
	[
		{
			"id": "blade_dancer_upg_alt",
			"squadValue": 200,
			"expBonus": 20,
			"tier": 3,
			"fraction": "dungeon",
			"nativeBiome": "Dirt",

			"ai": "range_type",
			
			"tags": [ "unit", "dungeon" ],

			"baseSid": "blade_dancer_upg",

			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 190 }
				]
			},
			
			"stats":
			{
				"hp": 16,
				"offence": 9,
				"defence": 7,
				"damageMin": 5,
				"damageMax": 7,

				"initiative": 6,
				"speed": 3,
				
				"luck": 0,
				"moral": 0,

				"energyPerCast": 1,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": -5,
				"moralMax": 5,
				"luckMin": -3,
				"luckMax": 3,

				"inDmgMods":
				{
					"list":
					[				
						
					]
				},

				"outDmgMods":
				{
					"list":
					[
									
					]
				}
			},
			
			"passives" : 
			[
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "cast", "tags": [ "living_immunities" ] }
						]
					}
				}
			],
			
			"defaultAttacks": 
			[
				{
					"attackType_" : "shoot",
					
					"selfMechanics" : [ ],
					
					"damageDealer" : 
					{
						"shootRange": -1,
						"shootThreshold": 3,
						"shootRedCount": 5,
						"shootDmgBuff": -0.1,
			
						"tags": [ "shoot_attack", "normal_damage", "basic_attack" ],
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"buffTarget_": "enemy",
						"buff":
						{
							"sid": "blade_dancer_debuff_2",
							"duration": -1
						},
						
						"targetMechanics" : [ ]
					}
				}
			],
				
			"alternativeAttacks":
			[
				{
					"attackType_" : "melee",

					"dontUseEnergy" : true,
					"cd" : -1,
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 0.5,

						"buffTarget_": "enemy",
						"buff":
						{
							"sid": "blade_dancer_debuff_2",
							"duration": -1
						},

						"targetMechanics" : [ ]
					}
				},
				{
					"attackType_" : "shoot",
					"rank": 1,
				
					"dontUseEnergy" : true,
					"cd" : -1,
					"selfMechanics" : [ ],
					
					"damageDealer" : 
					{
						"shootRange": -1,
						"shootThreshold": 3,
						"shootRedCount": 5,
						"shootDmgBuff": -0.1,
				
						"tags": [ "shoot_attack", "normal_damage", "basic_attack", "armed_ability", "armed_ability_blade_dancer" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",
				
						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},
				
						"multitargetType": "simultaneous",
						"numTargets": 2,
				
						"damageTarget_": "enemy",
						"damageType_": "normal",
				
						"statDmgMult": 0.5,

						"buffTarget_": "enemy",
						"buff":
						{
							"sid": "blade_dancer_debuff_2",
							"duration": -1
						},
						
						"targetMechanics" : [ ]
					}
				}
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 0.5,

						"buffTarget_": "enemy",
						"buff":
						{
							"sid": "blade_dancer_debuff_2",
							"duration": -1
						},

						"targetMechanics" : [ ]
					}
				}
			],
				
			"abilities" : 
			[ 

			]
		}
	]
}