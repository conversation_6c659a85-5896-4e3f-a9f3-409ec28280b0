﻿{
	"array":
	[
		{
			"id": "hive_queen_upg",
			"squadValue": 6045,
			"expBonus": 604,
			"tier": 7,
			"fraction": "demon",
			"nativeBiome": "Lava",

			"ai": "melee_type",
			
			"tags": [ "unit", "demon" ],

			"baseSid": "hive_queen",
			"upgradeSid": "hive_queen_upg_alt",
			
			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 3500 },
					{ "name": "crystals", "cost": 2  }
				]
			},

			"stats":
			{	
				"hp": 300,
				"offence": 30,
				"defence": 30,
				"damageMin": 50,
				"damageMax": 50,

				"initiative": 9,
				"speed": 6,
				
				"luck": 0,
				"moral": 0,

				"energyPerCast": 2,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": -5,
				"moralMax": 5,
				"luckMin": -5,
				"luckMax": 5,

				"damagePerDeltaLevelLower": 0.03,

				"inDmgMods":
				{
					"list":
					[

					]
				},

				"outDmgMods":
				{
					"list":
					[
									
					]
				}
			},
			
			"passives": 
			[
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "cast", "tags": [ "demon_immunities" ] }
						]
					}
				}
			],
			
			"conditionalPassives": 
			[
				{
					"condition": [ "unique_units_count", "ally", "demon" ],
					"stats": 
					{
						"defence": 1
					}
				},
				{
					"condition": [ "unique_units_count", "ally", "demon" ],
					"stats": 
					{
						"anticrit": 0.05
					}
				},
				{
					"condition": 
					[ 
						"unique_fractions_buff", 
						"enemy", 
						"human",	"hive_queen_passive_human", 
						"undead",	"hive_queen_passive_undead",
						"nature",	"hive_queen_passive_nature",
						"unfrozen",	"hive_queen_passive_unfrozen",
						"dungeon",	"hive_queen_passive_dungeon",
						"neutral",	"hive_queen_passive_neutral"
					]
				}
			],
			
			"defaultAttacks": 
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"triggerCounter" : true,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				} 
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,
						
						"targetMechanics" : [ ]
					}
				}
			],

			"abilities" : 
			[
				{
					"attackType_" : "cast",
					"rank": 4,
					
					"selfMechanics" : [ ],
												
					"cd" : 1,							
					"actionCost": 0,
					"energyLevel": 1,
					
					"damageDealer" : 
					{
						"instacast": true,
						"multitargetType": "simultaneous",
						
						"tags": [ "ability", "buff_ability" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_buff",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "ally",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "ally",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "none",

						"buffTarget_": "all",
						"buff":
						{
							"sid": "hive_queen_buff_null",
							"duration": 2
						},
						
						"targetMechanics" : [ { "mech": "copy_buff", "values": [ "conditional_passive", "2", "1", "2" ] } ]
					}
				},
				{
					"attackType_" : "cast",
					"rank": 4,
					
					"selfMechanics" : [ ],

					"cd" : 3,
					"energyLevel": 3,

					"damageDealer" : 
					{
						"tags": [ "ability", "magic_damage", "pure_damage" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_rumble_x2_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "all",
							"selection": "hexOrObject",
							"targetCondition_": "all"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "all",
							"selection": "object", 
							"targetCondition_": "alive" 
						},

						"damageTarget_": "all",
						"damageType_": "magic_pure",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				},
				{
					"attackType_" : "cast",
					"rank": 4,

					"selfMechanics" : [ ],

					"cd" : 2,
					"energyLevel": 2,

					"damageDealer" : 
					{
						"tags": [ "ability", "debuff_ability" ],
						"attackPatternSid" : "attack_single_buff",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "none",

						"buffTarget_": "enemy",
						"buff":
						{
							"sid": "hive_queen_debuff_parasite",
							"duration": 3
						},
						
						"targetMechanics" : [ ]
					},
					"allyDealer" : 
					{
						"tags": [ "ability", "buff_ability" ],
						"attackPatternSid" : "attack_single_buff",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "none",

						"buffTarget_": "ally",
						"buff":
						{
							"sid": "hive_queen_buff_parasite",
							"duration": 3
						},

						"targetMechanics" : [ ]
					}
				}
			]
		}

	]
}