﻿{
	"array":
	[
		{
			"id": "pandora_box",
			"aiIgnore": false,
			"variants": 
			[
				{
					"rollChance": 15,
					"value": 5000,

					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "SideResReward",
								"rewardShowType": "InWindow",
								"applyRewardFloating": true,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "resReward_notification",
								
								"parameters": [ "gold", "5000" ]
							}
						]
					}
				},
				{
					"rollChance": 15,
					"value": 10000,

					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "SideResReward",
								"rewardShowType": "InWindow",
								"applyRewardFloating": true,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "resReward_notification",

								"parameters": [ "gold", "10000" ]
							}
						]
					}
				},
				{
					"rollChance": 15,
					"value": 15000,

					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "SideResReward",
								"rewardShowType": "InWindow",
								"applyRewardFloating": true,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "resReward_notification",

								"parameters": [ "gold", "15000" ]
							}
						]
					}
				},
				{
					"rollChance": 15,
					"value": 20000,

					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "SideResReward",
								"rewardShowType": "InWindow",
								"applyRewardFloating": true,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "resReward_notification",

								"parameters": [ "gold", "20000" ]
							}
						]
					}
				},



				{
					"rollChance": 30,
					"value": 6000,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "HeroExpReward",
								"rewardShowType": "InWindow",
								"applyRewardFloating": true,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "expReward_notification",

								"parameters": [ "5000" ]
							}
						]
					}
				},
				{
					"rollChance": 30,
					"value": 12000,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "HeroExpReward",
								"rewardShowType": "InWindow",
								"applyRewardFloating": true,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "expReward_notification",

								"parameters": [ "10000" ]
							}
						]
					}
				},
				{
					"rollChance": 30,
					"value": 18000,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "HeroExpReward",
								"rewardShowType": "InWindow",
								"applyRewardFloating": true,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "expReward_notification",

								"parameters": [ "15000" ]
							}
						]
					}
				},
				{
					"rollChance": 30,
					"value": 24000,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "HeroExpReward",
								"rewardShowType": "InWindow",
								"applyRewardFloating": true,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "expReward_notification",

								"parameters": [ "20000" ]
							}
						]
					}
				},



				{
					"rollChance": 12,
					"value": 5000,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "HeroBoxUnitReward",
								"rewardShowType": "InWindow",
								"applyRewardFloating": true,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "unitReward_notification",

								"parameters": [ "1" ]
							}
						]
					}
				},
				{
					"rollChance": 12,
					"value": 7500,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "HeroBoxUnitReward",
								"rewardShowType": "InWindow",
								"applyRewardFloating": true,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "unitReward_notification",

								"parameters": [ "2" ]
							}
						]
					}
				},
				{
					"rollChance": 12,
					"value": 10500,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "HeroBoxUnitReward",
								"rewardShowType": "InWindow",
								"applyRewardFloating": true,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "unitReward_notification",

								"parameters": [ "3" ]
							}
						]
					}
				},
				{
					"rollChance": 12,
					"value": 14000,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "HeroBoxUnitReward",
								"rewardShowType": "InWindow",
								"applyRewardFloating": true,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "unitReward_notification",

								"parameters": [ "4" ]
							}
						]
					}
				},
				{
					"rollChance": 12,
					"value": 18000,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "HeroBoxUnitReward",
								"rewardShowType": "InWindow",
								"applyRewardFloating": true,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "unitReward_notification",

								"parameters": [ "5" ]
							}
						]
					}
				},
				{
					"rollChance": 12,
					"value": 22500,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "HeroBoxUnitReward",
								"rewardShowType": "InWindow",
								"applyRewardFloating": true,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "unitReward_notification",

								"parameters": [ "6" ]
							}
						]
					}
				},
				{
					"rollChance": 12,
					"value": 27500,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "HeroBoxUnitReward",
								"rewardShowType": "InWindow",
								"applyRewardFloating": true,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "unitReward_notification",

								"parameters": [ "7" ]
							}
						]
					}
				},



				{
					"rollChance": 5,
					"value": 8000,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "HeroStatsReward",
								"rewardShowType": "InWindow",
								"applyRewardFloating": true,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "statReward_notification",

								"parameters": [ "offence", "1", "defence", "1", "spellPower", "1", "intelligence", "1" ]
							}
						]
					}
				},



				{
					"rollChance": 5,
					"value": 16000,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "HeroStatsReward",
								"rewardShowType": "InWindow",
								"applyRewardFloating": true,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "statReward_notification",

								"parameters": [ "offence", "2", "defence", "2", "spellPower", "2", "intelligence", "2" ]
							}
						]
					}
				},



				{
					"rollChance": 5,
					"value": 24000,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "HeroStatsReward",
								"rewardShowType": "InWindow",
								"applyRewardFloating": true,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "statReward_notification",

								"parameters": [ "offence", "3", "defence", "3", "spellPower", "3", "intelligence", "3" ]
							}
						]
					}
				},



				{
					"rollChance": 5,
					"value": 32000,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "HeroStatsReward",
								"rewardShowType": "InWindow",
								"applyRewardFloating": true,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "statReward_notification",

								"parameters": [ "offence", "4", "defence", "4", "spellPower", "4", "intelligence", "4" ]
							}
						]
					}
				},



				{
					"rollChance": 10,
					"value": 20000,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "HeroMagicMassAdditionReward",
								"rewardShowType": "InWindow",
								"applyRewardFloating": false,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "spellReward_notification",

								"parameters": [ "day", "any", "any" ]
							}
						]
					}
				},
				{
					"rollChance": 10,
					"value": 20000,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "HeroMagicMassAdditionReward",
								"rewardShowType": "InWindow",
								"applyRewardFloating": false,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "spellReward_notification",

								"parameters": [ "night", "any", "any" ]
							}
						]
					}
				},
				{
					"rollChance": 10,
					"value": 20000,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "HeroMagicMassAdditionReward",
								"rewardShowType": "InWindow",
								"applyRewardFloating": false,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "spellReward_notification",

								"parameters": [ "space", "any", "any" ]
							}
						]
					}
				},
				{
					"rollChance": 10,
					"value": 20000,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "HeroMagicMassAdditionReward",
								"rewardShowType": "InWindow",
								"applyRewardFloating": false,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "spellReward_notification",

								"parameters": [ "primal", "any", "any" ]
							}
						]
					}
				},

				{
					"rollChance": 10,
					"value": 5000,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "HeroMagicMassAdditionReward",
								"rewardShowType": "InWindow",
								"applyRewardFloating": false,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "spellReward_notification",

								"parameters": [ "any", "any", "1" ]
							}
						]
					}
				},

				{
					"rollChance": 9,
					"value": 10000,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "HeroMagicMassAdditionReward",
								"rewardShowType": "InWindow",
								"applyRewardFloating": false,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "spellReward_notification",

								"parameters": [ "any", "any", "2" ]
							}
						]
					}
				},

				{
					"rollChance": 8,
					"value": 15000,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "HeroMagicMassAdditionReward",
								"rewardShowType": "InWindow",
								"applyRewardFloating": false,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "spellReward_notification",

								"parameters": [ "any", "any", "3" ]
							}
						]
					}
				},

				{
					"rollChance": 7,
					"value": 20000,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "HeroMagicMassAdditionReward",
								"rewardShowType": "InWindow",
								"applyRewardFloating": false,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "spellReward_notification",

								"parameters": [ "any", "any", "4" ]
							}
						]
					}
				},

				{
					"rollChance": 6,
					"value": 25000,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "HeroMagicMassAdditionReward",
								"rewardShowType": "InWindow",
								"applyRewardFloating": false,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "spellReward_notification",

								"parameters": [ "any", "any", "5" ]
							}
						]
					}
				},
				



				


				{
					"rollChance": 0,
					"value": 1500,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "HeroBoxUnitsReward",
								"rewardShowType": "InWindow",
								"applyRewardFloating": false,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "unitReward_notification",

								"parameters": [ "lich", "flicker", "avatar_of_war" ]
							}
						]
					}
				},
				{
					"rollChance": 0,
					"value": 2000,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "MovePointsAdditionReward",
								"rewardShowType": "Invisible",
								"applyRewardFloating": true,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "movePointsReward_notification",

								"parameters": [ "300" ]
							}
						]
					}
				},
				{
					"rollChance": 0,
					"value": 2000,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "ManaAdditionReward",
								"rewardShowType": "Invisible",
								"applyRewardFloating": true,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "manaReward_notification",

								"parameters": [ "1000", "true" ]
							}
						]
					}
				},
				{
					"rollChance": 0,
					"value": 2000,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "ManaPercentSettingReward",
								"rewardShowType": "Invisible",
								"applyRewardFloating": true,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "manaReward_notification",

								"parameters": [ "0.8" ]
							}
						]
					}
				},
				{
					"rollChance": 0,
					"value": 2000,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "HeroMagicAdditionReward",
								"rewardShowType": "InWindow",
								"applyRewardFloating": false,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "spellReward_notification",

								"parameters":
								[
									"night_2_magic_web",
									"primal_1_magic_thunderbolt",
									"primal_4_magic_fire_globe",
									"day_1_magic_healing_water",
									"day_8_magic_taunt",
									"primal_7_magic_wall_of_flame",
									"night_11_magic_vulnerability",
									"day_12_magic_radiant_armor",
									"night_16_magic_shadow_army"
								]
							}
						]
					}
				},
				{
					"rollChance": 0,
					"value": 2000,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "SideExpReward",
								"rewardShowType": "Invisible",
								"applyRewardFloating": true,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "sideExpReward_notification",

								"parameters": [ "1000" ]
							}
						]
					}
				},
				{
					"rollChance": 0,
					"value": 2000,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "SideExpToLevelUpReward",
								"rewardShowType": "Invisible",
								"applyRewardFloating": true,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "default",

								"parameters": [ "false" ]
							}
						]
					}
				},
				{
					"rollChance": 0,
					"value": 2000,
					
					"guardUnits": [],

					"rewardSet":
					{
						"rewardSetShowType": "Invisible",
						"rewardSetApplyType": "ForAll",
						"rewardSetCancelType": "CancelAndDelete",
						"label": "pandora_box_label",
						
						"rewards":
						[
							{
								"rewardType": "HeroRandomItemsReward",
								"rewardShowType": "Invisible",
								"applyRewardFloating": true,
								
								"rewardIcon": "default",
								"rewardName": "default",
								"rewardDesc": "default",
								"rewardNotificationDesc": "itemReward_notification",

								"parameters":
								[
									"common"
								]
							}
						]
					}
				}
			]
		}
	]
}