#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Скрипт для извлечения данных юнитов из JSON файлов
Извлекает id, squadValue, tier, fraction из всех файлов в units_logics/
"""

import json
import os
import glob

def extract_unit_data(json_file_path):
    """
    Извлекает данные юнита из JSON файла
    
    Args:
        json_file_path (str): Путь к JSON файлу
        
    Returns:
        list: Список словарей с данными юнитов
    """
    try:
        # Пробуем разные кодировки для обработки BOM
        encodings = ['utf-8-sig', 'utf-8', 'utf-16', 'cp1251']
        data = None

        for encoding in encodings:
            try:
                with open(json_file_path, 'r', encoding=encoding) as file:
                    data = json.load(file)
                break
            except (UnicodeDecodeError, json.JSONDecodeError):
                continue

        if data is None:
            raise Exception("Не удалось прочитать файл с поддерживаемыми кодировками")
            
        units = []
        
        # Проверяем структуру файла
        if 'array' in data and isinstance(data['array'], list):
            for unit in data['array']:
                if isinstance(unit, dict):
                    # Извлекаем нужные поля
                    unit_data = {}
                    
                    # Обязательные поля
                    if 'id' in unit:
                        unit_data['id'] = unit['id']
                    else:
                        continue  # Пропускаем юнит без id
                    
                    if 'squadValue' in unit:
                        unit_data['squadValue'] = unit['squadValue']
                    else:
                        continue  # Пропускаем юнит без squadValue
                    
                    # Опциональные поля
                    if 'tier' in unit:
                        unit_data['tier'] = unit['tier']
                    
                    if 'fraction' in unit:
                        unit_data['fraction'] = unit['fraction']
                    
                    units.append(unit_data)
        
        return units
        
    except (json.JSONDecodeError, FileNotFoundError, UnicodeDecodeError) as e:
        print(f"Ошибка при обработке файла {json_file_path}: {e}")
        return []

def find_all_json_files(base_dir):
    """
    Находит все JSON файлы в директории и поддиректориях
    
    Args:
        base_dir (str): Базовая директория для поиска
        
    Returns:
        list: Список путей к JSON файлам
    """
    json_files = []
    
    # Ищем все .json файлы рекурсивно
    pattern = os.path.join(base_dir, '**', '*.json')
    json_files = glob.glob(pattern, recursive=True)
    
    return json_files

def main():
    """
    Основная функция скрипта
    """
    base_dir = 'units_logics'
    output_file = 'fraction.txt'
    
    print(f"Поиск JSON файлов в директории: {base_dir}")
    
    # Проверяем существование директории
    if not os.path.exists(base_dir):
        print(f"Ошибка: Директория {base_dir} не найдена!")
        return
    
    # Находим все JSON файлы
    json_files = find_all_json_files(base_dir)
    
    if not json_files:
        print(f"JSON файлы не найдены в директории {base_dir}")
        return
    
    print(f"Найдено {len(json_files)} JSON файлов")
    
    all_units = []
    processed_files = 0
    
    # Обрабатываем каждый файл
    for json_file in json_files:
        print(f"Обрабатываем: {json_file}")
        units = extract_unit_data(json_file)
        
        if units:
            all_units.extend(units)
            processed_files += 1
            print(f"  Извлечено {len(units)} юнитов")
        else:
            print(f"  Юниты не найдены или ошибка")
    
    print(f"\nВсего обработано файлов: {processed_files}")
    print(f"Всего извлечено юнитов: {len(all_units)}")
    
    if not all_units:
        print("Нет данных для сохранения")
        return
    
    # Сортируем по id для удобства
    all_units.sort(key=lambda x: x['id'])
    
    # Формируем выходной JSON
    output_data = {
        "array": all_units
    }
    
    # Сохраняем в файл
    try:
        with open(output_file, 'w', encoding='utf-8') as file:
            # Записываем в красивом формате
            file.write('{\n  "array": [\n')
            
            for i, unit in enumerate(all_units):
                # Формируем строку для юнита
                unit_str = '    { '
                
                # Добавляем поля в определенном порядке
                fields = []
                fields.append(f'"id": "{unit["id"]}"')
                fields.append(f'"squadValue": {unit["squadValue"]}')
                
                if 'tier' in unit:
                    fields.append(f'"tier": {unit["tier"]}')
                
                if 'fraction' in unit:
                    fields.append(f'"fraction": "{unit["fraction"]}"')
                
                unit_str += ', '.join(fields)
                unit_str += ' }'
                
                # Добавляем запятую если не последний элемент
                if i < len(all_units) - 1:
                    unit_str += ','
                
                file.write(unit_str + '\n')
            
            file.write('  ]\n}')
        
        print(f"\nДанные успешно сохранены в файл: {output_file}")
        
        # Выводим статистику
        fractions = {}
        tiers = {}
        
        for unit in all_units:
            if 'fraction' in unit:
                fraction = unit['fraction']
                fractions[fraction] = fractions.get(fraction, 0) + 1
            
            if 'tier' in unit:
                tier = unit['tier']
                tiers[tier] = tiers.get(tier, 0) + 1
        
        print("\nСтатистика по фракциям:")
        for fraction, count in sorted(fractions.items()):
            print(f"  {fraction}: {count} юнитов")
        
        print("\nСтатистика по уровням:")
        for tier, count in sorted(tiers.items()):
            print(f"  Tier {tier}: {count} юнитов")
            
    except Exception as e:
        print(f"Ошибка при сохранении файла: {e}")

if __name__ == "__main__":
    main()
