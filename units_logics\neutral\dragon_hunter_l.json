﻿{
	"array":
	[
		{
			"id": "dragon_hunter",
			"squadValue": 1650,
			"expBonus": 165,
			"tier": 6,
			"fraction": "neutral",

			"ai": "melee_type",
			
			"tags": [ "unit", "neutral" ],

			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 1500 }
				]
			},

			"stats":
			{
				"hp": 85,
				"offence": 24,
				"defence": 20,
				"damageMin": 27,
				"damageMax": 36,

				"initiative": 7,
				"speed": 6,
				
				"luck": 0,
				"moral": 0,

				"energyPerCast": 2,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": 0,
				"moralMax": 0,
				"luckMin": 0,
				"luckMax": 0,

				"inDmgMods": 
				{
					"list":
					[

					]
				},

				"outDmgMods": 
				{
					"list": 
					[ 
						
					]
				},

				"outDmgModsByTargetTags": 
				{
					"list":
					[
						{ "t": "dragon", "v": 1.0 }
					]
				},

				"inDmgModsByCasterTags": 
				{
					"list":
					[

					]
				}
			},

			"passives": 
			[
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "cast", "tags": [ "living_immunities" ] }
						]
					}
				}
			],
			
			"defaultAttacks": 
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"triggerCounter" : true,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"buffTarget_": "enemy",
						"buff":
						{
							"sid": "dragon_hunter_blood",
							"duration": 2
						},

						"targetMechanics" : [ ]
					}
				} 
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"buffTarget_": "enemy",
						"buff":
						{
							"sid": "dragon_hunter_blood",
							"duration": 2
						},
						
						"targetMechanics" : [ ]
					}
				}
			],

			"abilities": 
			[
				{
					"attackType_" : "cast",
					"rank": 3,
					
					"selfMechanics" : [ ],

					"cd" : 3,
					"actionCost": 0,
					"energyLevel": 3,

					"damageDealer" : 
					{
						"instacast": true,
						"multitargetType": "simultaneous",
						
						"tags": [ "ability", "buff_ability" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_buff",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "self",
							"targetCondition_": "alive"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "self",
							"targetCondition_": "alive"
						},

						"damageTarget_": "none",

						"buffTarget_": "self",
						"buff":
						{
							"sid": "dragon_hunter_buff",
							"duration": 2
						},

						"targetMechanics" : [ ]
					}
				}
			]
		}
	]
}