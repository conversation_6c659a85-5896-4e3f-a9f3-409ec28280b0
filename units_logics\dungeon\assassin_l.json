﻿{
	"array":
	[
		{
			"id": "assassin",
			"squadValue": 92,
			"expBonus": 9,
			"tier": 2,
			"fraction": "dungeon",
			"nativeBiome": "Dirt",

			"ai": "melee_type",
			
			"tags": [ "unit", "dungeon" ],

			"upgradeSid": "assassin_upg",

			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 100 }
				]
			},
			
			"stats":
			{
				"hp": 13,
				"offence": 5,
				"defence": 3,
				"damageMin": 3,
				"damageMax": 5,

				"initiative": 4,
				"speed": 4,
				
				"luck": 1,
				"moral": 0,

				"energyPerCast": 2,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": -5,
				"moralMax": 5,
				"luckMin": -3,
				"luckMax": 5,

				"crit": 1.0,

				"inDmgMods":
				{
					"list":
					[				
						
					]
				},

				"outDmgMods":
				{
					"list":
					[
									
					]
				},

				"moveType" : "teleport"
			},
			
			"passives" : 
			[
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "cast", "tags": [ "living_immunities" ] }
						]
					}
				}
			],
			
			"defaultAttacks": 
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"triggerCounter" : true,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				} 
			],
				
			"alternativeAttacks" : 
			[ 
				{
					"attackType_" : "melee",
					"rank": 1,

					"dontUseEnergy" : true,
					"cd" : -1,
					"returnToStartAfterAttack": true,

					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "range_attack", "normal_damage", "basic_attack", "armed_ability", "armed_ability_assassin" ],
						"triggerCounter": true,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"tempSelfBuff": "assassin_selfbuff_3",

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 0.5,
						
						"targetMechanics" : [ ]
					}
				}
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,
						
						"targetMechanics" : [ ]
					}
				}
			],
				
			"abilities" : 
			[ 

			]
		}
	]
}