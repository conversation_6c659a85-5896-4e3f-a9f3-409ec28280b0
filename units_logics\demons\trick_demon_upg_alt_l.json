﻿{
	"array":
	[
		{
			"id": "trick_demon_upg_alt",
			"squadValue": 81,
			"expBonus": 8,
			"tier": 1,
			"fraction": "demon",
			"nativeBiome": "Lava",

			"ai": "reach_type",
			
			"tags": [ "unit", "demon" ],

			"baseSid": "trick_demon_upg",
			
			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 100 }
				]
			},

			"stats":
			{	
				"hp": 7,
				"offence": 6,
				"defence": 3,
				"damageMin": 2,
				"damageMax": 2,

				"initiative": 5,
				"speed": 5,
				
				"luck": 0,
				"moral": 0,

				"energyPerCast": 1,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": -5,
				"moralMax": 5,
				"luckMin": -5,
				"luckMax": 5,

				"damagePerDeltaLevel": 0.03,

				"inDmgMods":
				{
					"list":
					[

					]
				},

				"outDmgMods":
				{
					"list":
					[
									
					]
				}
			},

			"passives": 
			[
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "cast", "tags": [ "demon_immunities" ] }
						]
					}
				}
			],
			
			"conditionalPassives": 
			[
				{
					"condition": [ "unique_units_count", "ally", "demon" ],
					"stats": 
					{
						"offence": 1
					}
				},
				{
					"condition": [ "unique_units_count", "ally", "demon" ],
					"stats": 
					{
						"crit": 0.05
					}
				}
			],

			"defaultAttacks": 
			[
				{
					"attackType_" : "range",
							
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "range_attack", "normal_damage", "basic_attack" ],
						"triggerCounter" : true,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				} 
			],

			"alternativeAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"triggerCounter": true,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				}
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,
						
						"targetMechanics" : [ ]
					}
				}
			],

			"abilities": 
			[ 

			]
		}
	]
}