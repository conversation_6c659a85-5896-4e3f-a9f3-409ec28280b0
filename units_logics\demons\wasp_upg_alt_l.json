﻿{
	"array":
	[
		{
			"id": "wasp_upg_alt",
			"squadValue": 255,
			"expBonus": 25,
			"tier": 3,
			"fraction": "demon",
			"nativeBiome": "Lava",

			"ai": "melee_type",
			
			"tags": [ "unit", "demon" ],

			"baseSid": "wasp_upg",
			
			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 250 }
				]
			},

			"stats":
			{	
				"hp": 16,
				"offence": 10,
				"defence": 10,
				"damageMin": 4,
				"damageMax": 8,

				"initiative": 8,
				"speed": 7,
				
				"luck": 0,
				"moral": 0,

				"energyPerCast": 2,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": -5,
				"moralMax": 5,
				"luckMin": -5,
				"luckMax": 5,

				"inDmgMods": 
				{
					"list": 
					[ 

					]
				},

				"outDmgMods": 
				{
					"list": 
					[ 

					]
				},
				
				"moveType": "fly"
			},

			"passives": 
			[
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "cast", "tags": [ "demon_immunities" ] }
						]
					}
				}
			],
			
			"conditionalPassives": 
			[
				{
					"condition": [ "unique_units_count", "ally", "demon" ],
					"stats": 
					{
						"offence": 1
					}
				},
				{
					"condition": [ "unique_units_count", "ally", "demon" ],
					"stats": 
					{
						"crit": 0.05
					}
				}
			],

			"defaultAttacks": 
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"triggerCounter" : true,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ { "mech": "kill_stacks", "values": [ "1", "6" ] } ]
					}
				} 
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,
						
						"targetMechanics" : [ { "mech": "kill_stacks", "values": [ "1", "6" ] } ]
					}
				}
			],
					
			"abilities": [ ]
		}

	]
}