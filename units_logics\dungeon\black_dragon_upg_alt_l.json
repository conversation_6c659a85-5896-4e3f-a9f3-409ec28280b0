﻿{
	"array":
	[
		{
			"id": "black_dragon_upg_alt",
			"squadValue": 8660,
			"expBonus": 866,
			"tier": 7,
			"fraction": "dungeon",
			"nativeBiome": "Dirt",

			"ai": "melee_type",
			
			"tags": [ "unit", "dungeon", "dragon" ],

			"baseSid": "black_dragon_upg",

			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 5000 },
					{ "name": "gemstones", "cost": 2  }
				]
			},
			
			"stats":
			{
				"hp": 350,
				"offence": 36,
				"defence": 30,
				"damageMin": 60,
				"damageMax": 65,

				"initiative": 8,
				"speed": 9,
				
				"luck": 0,
				"moral": 0,

				"energyPerCast": 2,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": 0,
				"moralMax": 5,
				"luckMin": 0,
				"luckMax": 3,

				"inDmgMods":
				{
					"list":
					[				
						
					]
				},

				"outDmgMods":
				{
					"list":
					[
									
					]
				},

				"moveType": "fly"
			},
			
			"passives" : 
			[
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "magic_level", "tags": [ "5" ] },
							
							{ "type": "ability_rank", "tags": [ "5" ] },
							
							{ "type": "cast", "tags": [ "dragon_immunities" ] }
						]
					}
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "effect", "tags": [ "overTime_effect_poison" ] },
							{ "type": "effect", "tags": [ "buff_effect_poison" ] }
						]
					}
				}
			],

			"aura":
			{
				"tag": "black_dragon_aura",
				"target": "enemy",
				"power": 1,
				"data":
				{
					"stats":
					{
						"alwaysMinDmg": true
					}
				}
			},
			
			"defaultAttacks": 
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"triggerCounter" : true,
						"attackPatternSid" : "attack_reach_x1_x100_x100_with_delay",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "all",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				} 
			],
				
			"alternativeAttacks" : 
			[ 
				{
					"attackType_" : "melee",
					"rank": 1,

					"dontUseEnergy" : true,
					"cd" : -1,
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "armed_ability", "armed_ability_black_dragon" ],
						"triggerCounter" : true,
						"attackPatternSid" : "attack_massive_x1_x100_x100_with_dalay_black_dragon",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "all",
						"damageType_": "normal",

						"statDmgMult": 0.5,
						
						"targetMechanics" : [ ]
					}
				}
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_reach_x1_x100_x100_with_delay",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "all",
						"damageType_": "normal",

						"statDmgMult": 1.0,
						
						"targetMechanics" : [ ]
					}
				}
			],
				
			"abilities" : 
			[ 
				{
					"attackType_" : "cast",
					"rank": 10,
					
					"selfMechanics" : [ ],

					"cd" : 2,
					"actionCost": 0,
					"energyLevel": 2,

					"damageDealer" : 
					{
						"instacast": true,
						"multitargetType": "simultaneous",
						
						"tags": [ "ability", "buff_ability", "charge_ability" ],
						"triggerCounter" : false,
						"attackPatternSid" : "attack_single_buff",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "self",
							"targetCondition_": "alive"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "self",
							"targetCondition_": "alive"
						},

						"damageTarget_": "none",

						"buffTarget_": "self",
						"buff":
						{
							"sid": "black_dragon_buff",
							"duration": 2
						},

						"targetMechanics" : [ ]
					}
				},
				{
					"attackType_" : "end_move_attack",
					"moveType": "teleport",
					"rank": 5,
					
					"selfMechanics" : [ ],
					
					"cd" : 2,
					"energyLevel": 2,
					
					"damageDealer" : 
					{
						"tags": [ "ability", "armed_ability", "normal_damage", "basic_attack", "melee_attack" ],

						"shootRange": 99,

						"triggerCounter": false,
						"attackPatternSid" : "attack_swirl_x1_x100",

						"castTargetParams":   
						{ 
							"targetTags": [  ],
							"castTarget_": "none",
							"selection": "hex"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "noself",
							"selection": "object"
						},

						"damageTarget_": "all",
						"damageType_": "normal",
						
						"targetMechanics": [ { "mech": "push_unit", "values": [ "false" ] } ]
					}
				}
			]
		}
	]
}