﻿{
	"array":
	[
		{
			"id": "assassin_upg",
			"squadValue": 124,
			"expBonus": 12,
			"tier": 2,
			"fraction": "dungeon",
			"nativeBiome": "Dirt",

			"ai": "melee_type",
			
			"tags": [ "unit", "dungeon" ],

			"baseSid": "assassin",
			"upgradeSid": "assassin_upg_alt",

			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 130 }
				]
			},
			
			"stats":
			{
				"hp": 13,
				"offence": 5,
				"defence": 3,
				"damageMin": 3,
				"damageMax": 5,

				"initiative": 4,
				"speed": 6,
				
				"luck": 1,
				"moral": 0,

				"energyPerCast": 2,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": -5,
				"moralMax": 5,
				"luckMin": -3,
				"luckMax": 5,
				
				"crit": 1.0,

				"inDmgMods":
				{
					"list":
					[				
						
					]
				},

				"outDmgMods":
				{
					"list":
					[
									
					]
				},

				"moveType" : "teleport"
			},
			
			"passives" : 
			[
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "cast", "tags": [ "living_immunities" ] }
						]
					}
				}
			],
			
			"defaultAttacks": 
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"triggerCounter" : true,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"buffTarget_": "enemy",
						"buff":
						{
							"sid": "assassin_debuff",
							"duration": 2
						},

						"targetMechanics" : [ ]
					}
				}
			],
				
			"alternativeAttacks" : 
			[ 
				{
					"attackType_" : "melee",
					"rank": 1,

					"dontUseEnergy" : true,
					"cd" : -1,
					"returnToStartAfterAttack": true,

					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "range_attack", "normal_damage", "basic_attack", "armed_ability", "armed_ability_assassin" ],
						"triggerCounter": true,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 0.5,

						"buffTarget_": "enemy",
						"buff":
						{
							"sid": "assassin_debuff",
							"duration": 2
						},

						"tempSelfBuff": "assassin_selfbuff_3",

						"targetMechanics" : [ ]
					}
				}
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"buffTarget_": "enemy",
						"buff":
						{
							"sid": "assassin_debuff",
							"duration": 2
						},
						
						"targetMechanics" : [ ]
					}
				}
			],
				
			"abilities" : 
			[ 
				{
					"attackType_" : "cast",
					"rank": 3,
					
					"selfMechanics" : [ ],

					"cd" : 2,
					"energyLevel": 2,
					
					"damageDealer" : 
					{
						"tags": [ "normal_damage", "ability" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"tempSelfBuff": "assassin_selfbuff",

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"buffTarget_": "enemy",
						"buff":
						{
							"sid": "assassin_debuff",
							"duration": 2
						},

						"targetMechanics" : [ ]
					}
				}
			]
		}
	]
}