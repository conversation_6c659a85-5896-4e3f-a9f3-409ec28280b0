﻿{
	"array":
	[
		
		{
			"id": "griffin_upg_alt",
			"squadValue": 315,
			"expBonus": 31,
			"tier": 3,
			"fraction": "human",
			"nativeBiome": "Grass",

			"ai": "melee_type",
			
			"tags": [ "unit", "human" ],

			"baseSid": "griffin_upg",

			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 300 }
				]
			},
			
			"stats":
			{
				"hp": 30,
				"offence": 10,
				"defence": 10,
				"damageMin": 7,
				"damageMax": 7,

				"initiative": 6,
				"speed": 5,
				
				"luck": 0,
				"moral": 0,

				"energyPerCast": 2,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,				
				"actionPoints": 1,
				"numCounters": 2,

				"moralMin": -3,
				"moralMax": 3,
				"luckMin": -5,
				"luckMax": 5,

				"inDmgMods":
				{
					"list":
					[
						
					]
				},

				"outDmgMods":
				{
					"list":
					[
									
					]
				},
				
				"moveType": "fly"
			},
			
			"passives" : 
			[
				{
					"data": 
					{
						"sequenceEffect": "assist_ability"
					}
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "cast", "tags": [ "magic_creature_immunities" ] }
						]
					}
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "effect", "tags": [ "overTime_effect_curse" ] }
						]
					}
				}
			],
			
			"defaultAttacks": 
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"triggerCounter" : true,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				} 
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,
						
						"targetMechanics" : [ ]
					}
				}
			],

			"abilities": 
			[
				{
					"attackType_" : "cast",
					"rank": 3,
					
					"selfMechanics" : [ ],

					"cd" : 2,
					"actionCost": 0,
					"energyLevel": 2,

					"damageDealer" : 
					{
						"instacast": true,
						"multitargetType": "simultaneous",
						
						"tags": [ "ability", "buff_ability", "charge_ability" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_buff",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "self",
							"targetCondition_": "alive"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "self",
							"targetCondition_": "alive"
						},

						"damageTarget_": "none",

						"buffTarget_": "self",
						"buff":
						{
							"sid": "griffin_selfbuff_2",
							"duration": 2
						},

						"targetMechanics" : [ ]
					}
				}
			]
		}
	]
}