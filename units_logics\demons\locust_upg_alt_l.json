﻿{
	"array":
	[
		{
			"id": "locust_upg_alt",
			"squadValue": 160,
			"expBonus": 16,
			"tier": 2,
			"fraction": "demon",
			"nativeBiome": "Lava",

			"ai": "melee_type",
			
			"tags": [ "unit", "demon" ],

			"baseSid": "locust_upg",
			
			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 170 }
				]
			},

			"stats":
			{	
				"hp": 12,
				"offence": 7,
				"defence": 4,
				"damageMin": 4,
				"damageMax": 4,

				"initiative": 6,
				"speed": 7,
				
				"luck": 0,
				"moral": 0,

				"energyPerCast": 2,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": -5,
				"moralMax": 5,
				"luckMin": -5,
				"luckMax": 5,

				"inDmgMods":
				{
					"list":
					[

					]
				},

				"outDmgMods":
				{
					"list":
					[
									
					]
				}
			},
			
			"passives": 
			[
				{
					"data":
					{
						"sequenceEffect": "add_attack_after_counter_melee"
					}
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "cast", "tags": [ "demon_immunities" ] }
						]
					}
				}
			],
			
			"conditionalPassives": 
			[
				{
					"condition": [ "unique_units_count", "ally", "demon" ],
					"stats": 
					{
						"offence": 1
					}
				},
				{
					"condition": [ "unique_units_count", "ally", "demon" ],
					"stats": 
					{
						"crit": 0.05
					}
				}
			],
			
			"defaultAttacks": 
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"triggerCounter" : true,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				} 
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,
						
						"targetMechanics" : [ ]
					}
				}
			],
				
			"abilities" : 
			[ 
				{
					"attackType_" : "jump",
					"rank": 2,
					
					"selfMechanics" : [ ],
					
					"cd" : 1,
					"actionCost": 0,
					"energyLevel": 1,
					
					"damageDealer" : 
					{
						"tags": [ "ability", "buff_ability", "movement_ability", "undead_immunities", "embodiment_immunities", "construct_immunities" ],

						"shootRange": 6,

						"attackPatternSid" : "attack_single_buff",
						"triggerCounter": false,

						"castTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "all",
							"targetCondition_": "dead",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "all",
							"targetCondition_": "dead",
							"selection": "object"
						},

						"damageTarget_": "none",
						"damageType_": "none",

						"targetMechanics" : 
						[ 
							{
								"mech": "destroy_corpse", "values":  [ ]
							}
						]
					},
					"selfDealer" : 
					{
						"attackPatternSid" : "attack_single_buff",
						"triggerCounter": false,

						"castTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "self",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "self",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "none",

						"buffTarget_": "self",
						"buff":
						{
							"sid": "locust_selfbuff_2",
							"duration": -1
						},
						
						"targetMechanics" : [ ]
					}
				}
			]
		}
	]
}