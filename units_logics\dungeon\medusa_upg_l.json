﻿{
	"array":
	[
		{
			"id": "medusa_upg",
			"squadValue": 643,
			"expBonus": 64,
			"tier": 5,
			"fraction": "dungeon",
			"nativeBiome": "Dirt",

			"ai": "range_type",
			
			"tags": [ "unit", "dungeon" ],

			"baseSid": "medusa",
			"upgradeSid": "medusa_upg_alt",

			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 530 }
				]
			},
			
			"stats":
			{
				"hp": 45,
				"offence": 16,
				"defence": 16,
				"damageMin": 15,
				"damageMax": 15,

				"initiative": 4,
				"speed": 4,
				
				"luck": 0,
				"moral": 0,

				"energyPerCast": 1,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": -5,
				"moralMax": 5,
				"luckMin": -3,
				"luckMax": 3,

				"inDmgMods":
				{
					"list":
					[				
						
					]
				},

				"outDmgMods":
				{
					"list":
					[
									
					]
				},

				"ignoreShootDmgBuff": true
			},
			
			"passives" : 
			[
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "cast", "tags": [ "magic_creature_immunities" ] }
						]
					}
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "effect", "tags": [ "overTime_effect_curse" ] }
						]
					}
				}
			],
			
			"defaultAttacks": 
			[
				{
					"attackType_" : "shoot",
					
					"selfMechanics" : [ ],
					
					"damageDealer" : 
					{
						"shootRange": -1,
						"shootThreshold": 3,
						"shootRedCount": 5,
						"shootDmgBuff": -0.1,
			
						"tags": [ "shoot_attack", "normal_damage", "basic_attack" ],
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,
						
						"targetMechanics" : [ ]
					}
				}
			],
				
			"alternativeAttacks":
			[
				{
					"attackType_" : "melee",

					"dontUseEnergy" : true,
					"cd" : -1,
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				},
				{
					"attackType_" : "shoot",
					"rank": 1,

					"dontUseEnergy" : true,
					"cd" : -1,
					"selfMechanics" : [ ],
					
					"damageDealer" : 
					{
						"shootRange": -1,
						"shootThreshold": 3,
						"shootRedCount": 5,
						"shootDmgBuff": -0.1,
			
						"tags": [ "shoot_attack", "normal_damage", "basic_attack", "armed_ability", "armed_ability_medusa" ],
						"triggerCounter": true,
						"attackPatternSid" : "attack_rumble_x1_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 0.5,
						
						"targetMechanics" : [ ]
					}
				}
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				}
			],
				
			"abilities" : 
			[ 
				{
					"attackType_" : "move",
					"moveType": "walk",
					"rank": 2,
										
					"selfMechanics" : [ ],

					"cd" : 1,
					"actionCost": 0,
					"energyLevel": 1,
					
					"damageDealer" : 
					{
						"tags": [ "ability", "movement_ability" ],

						"useSpeedAsRange": true,

						"triggerCounter": false,
						"attackPatternSid" : "attack_single_buff",

						"castTargetParams":   
						{ 
							"selection": "hex",
							"castTarget_": "all"
						},

						"affectTargetParams":   
						{ 
							"selection": "hex",
							"castTarget_": "all"
						},

						"damageTarget_": "none",

						"targetMechanics" : [ ]
					}
				},
				{
					"attackType_" : "melee",
					"rank": 2,
					
					"selfMechanics" : [ ],
															
					"cd" : 2,
					"energyLevel": 2,
					
					"damageDealer" : 
					{
						"tags": [ "ability", "movement_ability", "embodiment_immunities" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "none",

						"buffTarget_": "enemy",
						"buff":
						{
							"sid": "medusa_stone",
							"duration": 1
						},
						
						"targetMechanics" : [  ]
					}
				}
			]
		}
	]
}