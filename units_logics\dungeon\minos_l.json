﻿{
	"array":
	[
		{
			"id": "minos",
			"squadValue": 403,
			"expBonus": 40,
			"tier": 4,
			"fraction": "dungeon",
			"nativeBiome": "Dirt",

			"ai": "melee_type",
			
			"tags": [ "unit", "dungeon" ],

			"upgradeSid": "minos_upg",

			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 370 }
				]
			},
			
			"stats":
			{
				"hp": 50,
				"offence": 12,
				"defence": 12,
				"damageMin": 11,
				"damageMax": 14,

				"initiative": 4,
				"speed": 4,
				
				"luck": 0,
				"moral": 2,

				"energyPerCast": 2,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": 0,
				"moralMax": 5,
				"luckMin": -5,
				"luckMax": 5,

				"additionalActionChanceModifier": 0.05,

				"inDmgMods":
				{
					"list":
					[				
						
					]
				},

				"outDmgMods":
				{
					"list":
					[
									
					]
				}
			},
			
			"passives" : 
			[
				{
					"actions":
					[
						{
							"trigger": "unit_took_damage",
							"triggerConditions": 
							[
								{
									"checkFunction": "allegiance",
									"values": [ "self" ]
								}
							],

							"damageDealer": 
							{
								"tags": [ "passive" ],
								"triggerCounter": false,

								"damageTarget_": "none",
								"statDmgMult": 0.0,

								"attackPatternSid": "attack_single_x100",

								"castTargetParams":   
								{ 
									"targetTags": [ "unit" ],
									"castTarget_": "all",
									"selection": "object"
								},

								"affectTargetParams":   
								{ 
									"targetTags": [ "unit" ],
									"castTarget_": "all",
									"selection": "object"
								},

								"buffTarget_": "ally",
								"buff":
								{
									"sid": "minos_selfbuff",
									"duration": -1
								},
								
								"targetMechanics" : [ ]
							}
						}
					]
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "cast", "tags": [ "magic_creature_immunities" ] }
						]
					}
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "effect", "tags": [ "overTime_effect_curse" ] }
						]
					}
				}
			],
			
			"defaultAttacks": 
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"triggerCounter" : true,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				} 
			],
				
			"alternativeAttacks" : 
			[ 
				{
					"attackType_" : "melee",
					"rank": 1,

					"dontUseEnergy" : true,
					"cd" : -1,
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "armed_ability", "armed_ability_minos" ],
						"triggerCounter": true,
						"attackPatternSid" : "attack_reach_x2_x100_x100_x100_with_delay",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "all",
						"damageType_": "normal",

						"statDmgMult": 0.5,
						
						"targetMechanics" : [ ]
					}
				}
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,
						
						"targetMechanics" : [ ]
					}
				}
			],
				
			"abilities" : 
			[ 

			]
		}
	]
}