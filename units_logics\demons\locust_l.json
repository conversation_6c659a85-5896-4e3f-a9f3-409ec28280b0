{"array": [{"id": "locust", "squadValue": 119, "expBonus": 11, "tier": 2, "fraction": "demon", "nativeBiome": "<PERSON><PERSON>", "ai": "melee_type", "tags": ["unit", "demon"], "upgradeSid": "locust_upg", "unitCost": {"costResArray": [{"name": "gold", "cost": 125}]}, "stats": {"hp": 12, "offence": 5, "defence": 4, "damageMin": 3, "damageMax": 4, "initiative": 5, "speed": 5, "luck": 0, "moral": 0, "energyPerCast": 2, "energyPerRound": 0, "energyPerTakeDamage": 1, "actionPoints": 1, "numCounters": 1, "moralMin": -5, "moralMax": 5, "luckMin": -5, "luckMax": 5, "inDmgMods": {"list": []}, "outDmgMods": {"list": []}}, "passives": [{"data": {"sequenceEffect": "add_attack_after_counter_melee"}}, {"data": {"immunities": [{"type": "cast", "tags": ["demon_immunities"]}]}}], "defaultAttacks": [{"attackType_": "melee", "selfMechanics": [], "damageDealer": {"tags": ["melee_attack", "normal_damage", "basic_attack"], "triggerCounter": true, "attackPatternSid": "attack_single_x100", "castTargetParams": {"targetTags": ["unit", "attackable_object"], "castTarget_": "enemy", "targetCondition_": "alive", "selection": "object"}, "affectTargetParams": {"targetTags": ["unit", "attackable_object"], "castTarget_": "enemy", "targetCondition_": "alive", "selection": "object"}, "damageTarget_": "enemy", "damageType_": "normal", "statDmgMult": 1.0, "targetMechanics": []}}], "counterAttacks": [{"attackType_": "melee", "selfMechanics": [], "damageDealer": {"tags": ["melee_attack", "normal_damage", "basic_attack", "counter_attack"], "triggerCounter": false, "attackPatternSid": "attack_single_x100", "castTargetParams": {"targetTags": ["unit", "attackable_object"], "castTarget_": "all", "targetCondition_": "alive", "selection": "object"}, "affectTargetParams": {"targetTags": ["unit", "attackable_object"], "castTarget_": "all", "targetCondition_": "alive", "selection": "object"}, "damageTarget_": "enemy", "damageType_": "normal", "statDmgMult": 1.0, "targetMechanics": []}}], "abilities": [{"attackType_": "jump", "rank": 2, "selfMechanics": [], "cd": 1, "actionCost": 0, "energyLevel": 1, "damageDealer": {"tags": ["ability", "buff_ability", "movement_ability", "undead_immunities", "embodiment_immunities", "construct_immunities"], "shootRange": 3, "attackPatternSid": "attack_single_buff", "triggerCounter": false, "castTargetParams": {"targetTags": ["unit"], "castTarget_": "all", "targetCondition_": "dead", "selection": "object"}, "affectTargetParams": {"targetTags": ["unit"], "castTarget_": "all", "targetCondition_": "dead", "selection": "object"}, "damageTarget_": "none", "damageType_": "none", "targetMechanics": [{"mech": "destroy_corpse", "values": []}]}, "selfDealer": {"attackPatternSid": "attack_single_buff", "triggerCounter": false, "castTargetParams": {"targetTags": ["unit"], "castTarget_": "self", "targetCondition_": "alive", "selection": "object"}, "affectTargetParams": {"targetTags": ["unit"], "castTarget_": "self", "targetCondition_": "alive", "selection": "object"}, "damageTarget_": "none", "buffTarget_": "self", "buff": {"sid": "locust_selfbuff_1", "duration": -1}, "targetMechanics": []}}]}]}