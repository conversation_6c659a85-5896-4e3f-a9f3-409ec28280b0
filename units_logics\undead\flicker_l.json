﻿{
	"array":
	[
		{
			"id": "flicker",
			"squadValue": 77,
			"expBonus": 7,
			"tier": 2,
			"fraction": "undead",
			"nativeBiome": "Deathland",

			"ai": "melee_type",
			
			"tags": [ "unit", "undead" ],
			
			"upgradeSid": "flicker_upg",

			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 80 }
				]
			},
			
			"stats":
			{
				"hp": 9,
				"offence": 3,
				"defence": 0,
				"damageMin": 2,
				"damageMax": 4,

				"initiative": 4,
				"speed": 3,
				
				"luck": 0,
				"moral": 0,

				"energyPerCast": 2,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": 0,
				"moralMax": 0,
				"luckMin": -5,
				"luckMax": 5,

				"inDmgMods":
				{
					"list":
					[
						
					]
				},

				"outDmgMods":
				{
					"list":
					[
									
					]
				},
				
				"moveType": "fly"
			},
			
			"passives" : 
			[
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "cast", "tags": [ "undead_immunities" ] }
						]
					}
				},
				{
					"data": 
					{
						"disablers": 
						[ 
							{ "mech": "vampirism" },
							{ "mech": "destroy_corpse_and_heal" }
						]
					}
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "effect", "tags": [ "overTime_effect_curse" ] },
							{ "type": "effect", "tags": [ "overTime_effect_blood" ] },
							{ "type": "effect", "tags": [ "overTime_effect_poison" ] },
							{ "type": "effect", "tags": [ "buff_effect_poison" ] }
						]
					}
				}
			],
			
			"defaultAttacks": 
			[
				{	
					"attackType_" : "melee",

					"selfMechanics" : 
					[ 
						{ 
							"mech": "vampirism", "values": [ "0" , "0.2" , "self" , "with temp stacks" ], "afterAttack": true 
						}
					],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"triggerCounter" : true,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				}
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",

					"selfMechanics" : 
					[ 
						{ 
							"mech": "vampirism", "values": [ "0" , "0.2" , "self" , "with temp stacks" ], "afterAttack": true 
						}
					],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,
						
						"targetMechanics" : [ ]
					}
				}
			],
				
			"abilities" : [ ]
		}
	]
}