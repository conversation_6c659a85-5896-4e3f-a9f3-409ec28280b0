﻿{
	"array":
	[
		{
			"id": "phoenix",
			"squadValue": 4415,
			"expBonus": 441,
			"tier": 7,
			"fraction": "nature",
			"nativeBiome": "Autumn",

			"ai": "melee_type",
			
			"tags": [ "unit", "nature" ],

			"upgradeSid": "phoenix_upg",

			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 2700 },
					{ "name": "crystals", "cost": 1  }
				]
			},

			"leaveCorpse": false,

			"stats":
			{	
				"hp": 250,
				"offence": 28,
				"defence": 24,
				"damageMin": 50,
				"damageMax": 70,

				"initiative": 10,
				"speed": 6,
				
				"luck": 0,
				"moral": 0,

				"energyPerCast": 2,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": -3,
				"moralMax": 3,
				"luckMin": -3,
				"luckMax": 3,

				"inDmgMods": 
				{
					"list":
					[
						
					]
				},

				"outDmgMods": 
				{
					"list": 
					[ 

					]
				},

				"moveType":"fly"
			},
			
			"passives": 
			[
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "damage", "tags": [ "primal_4_magic_fire_globe_damage", "primal_7_magic_wall_of_flame_damage", "primal_11_magic_armageddon_damage" ] }
						]
					}
				},
				{
					"actions":
					[
						{
							"trigger": "start_battle",
							"triggerConditions": 
							[
								{
									"checkFunction": "allegiance",
									"values": [ "self" ]
								}
							],

							"damageDealer": 
							{
								"tags": [ "passive" ],
								"triggerCounter": false,

								"damageTarget_": "none",
								"statDmgMult": 0.0,

								"attackPatternSid": "attack_single_x100",

								"castTargetParams":   
								{ 
									"targetTags": [ "unit" ],
									"castTarget_": "all",
									"selection": "object"
								},

								"affectTargetParams":   
								{ 
									"targetTags": [ "unit" ],
									"castTarget_": "all",
									"selection": "object"
								},

								"buffTarget_": "ally",
								"buff":
								{
									"sid": "wisp_silence",
									"duration": -1
								},
								
								"targetMechanics" : [ ]
							}
						}
					]
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "cast", "tags": [ "embodiment_immunities" ] }
						]
					}
				},
				{
					"data": 
					{
						"disablers": 
						[ 
							{ "mech": "vampirism" },
							{ "mech": "destroy_corpse_and_heal" }
						]
					}
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "effect", "tags": [ "overTime_effect_fire" ] },
							{ "type": "effect", "tags": [ "overTime_effect_blood" ] },
							{ "type": "effect", "tags": [ "overTime_effect_poison" ] },
							{ "type": "effect", "tags": [ "buff_effect_poison" ] }
						]
					}
				}
			],
			
			"defaultAttacks": 
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"triggerCounter" : true,
						"attackPatternSid" : "attack_swipe_x100_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "all",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				} 
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_swipe_x100_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "all",
						"damageType_": "normal",

						"statDmgMult": 1.0,
						
						"targetMechanics" : [ ]
					}
				}
			],

			"abilities": 
			[ 
				{
					"attackType_" : "cast",
					"rank": 6,
					
					"selfMechanics" : [ { "mech": "dispel", "values":  [ "wisp_silence" ] } ],

					"cd" : 1,
					"charges" : 1,
					"actionCost": 0,
					"neverDisable" : true,

					"energyLevel": 3,

					"damageDealer" : 
					{
						"instacast": true,
						"multitargetType": "simultaneous",
						
						"tags": [ "ability", "buff_ability" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_buff",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "self",
							"targetCondition_": "alive"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "self",
							"targetCondition_": "alive"
						},

						"damageTarget_": "none",

						"buffTarget_": "self",
						"buff":
						{
							"sid": "phoenix_wisp_buff_1",
							"duration": 999
						},

						"targetMechanics" : [ ]
					}
				}
			]
		}
	]
}