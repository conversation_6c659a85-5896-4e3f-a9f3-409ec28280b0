﻿{
	"array":
	[
		{
			"id": "assassin_upg_alt",
			"squadValue": 124,
			"expBonus": 12,
			"tier": 2,
			"fraction": "dungeon",
			"nativeBiome": "Dirt",

			"ai": "melee_type",
			
			"tags": [ "unit", "dungeon" ],

			"baseSid": "assassin_upg",

			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 130 }
				]
			},
			
			"stats":
			{
				"hp": 13,
				"offence": 6,
				"defence": 3,
				"damageMin": 3,
				"damageMax": 5,

				"initiative": 5,
				"speed": 5,
				
				"luck": 2,
				"moral": 0,

				"energyPerCast": 2,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": -5,
				"moralMax": 5,
				"luckMin": -3,
				"luckMax": 5,
				
				"crit": 1.0,

				"inDmgMods":
				{
					"list":
					[				
						
					]
				},

				"outDmgMods":
				{
					"list":
					[
									
					]
				},

				"moveType" : "teleport"
			},
			
			"passives" : 
			[
				{
					"actions":
					[
						{
							"trigger": "unit_cast_ability",
							"triggerConditions": 
							[
								{
									"checkFunction": "ability_and_allegiance",
									"values": [ "self", "defaultAttacks" ]
								}
							],

							"damageDealer": 
							{
								"tags": [ "passive" ],
								"triggerCounter": false,

								"damageTarget_": "none",
								"statDmgMult": 0.0,

								"attackPatternSid": "attack_single_x100",

								"castTargetParams":   
								{ 
									"targetTags": [ "unit" ],
									"castTarget_": "all",
									"selection": "object"
								},

								"affectTargetParams":   
								{ 
									"targetTags": [ "unit" ],
									"castTarget_": "all",
									"selection": "object"
								},

								"buffTarget_": "ally",
								"buff":
								{
									"sid": "assassin_selfbuff_2",
									"duration": -1
								},
								
								"targetMechanics" : [ ]
							}
						},
						{
							"trigger": "unit_cast_ability",
							"triggerConditions": 
							[
								{
									"checkFunction": "ability_and_allegiance",
									"values": [ "self", "counterAttacks" ]
								}
							],

							"damageDealer": 
							{
								"tags": [ "passive" ],
								"triggerCounter": false,

								"damageTarget_": "none",
								"statDmgMult": 0.0,

								"attackPatternSid": "attack_single_x100",

								"castTargetParams":   
								{ 
									"targetTags": [ "unit" ],
									"castTarget_": "all",
									"selection": "object"
								},

								"affectTargetParams":   
								{ 
									"targetTags": [ "unit" ],
									"castTarget_": "all",
									"selection": "object"
								},

								"buffTarget_": "ally",
								"buff":
								{
									"sid": "assassin_selfbuff_2",
									"duration": -1
								},
								
								"targetMechanics" : [ ]
							}
						}
					]
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "cast", "tags": [ "living_immunities" ] }
						]
					}
				}
			],
			
			"defaultAttacks": 
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"triggerCounter" : true,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				} 
			],
				
			"alternativeAttacks" : 
			[ 
				{
					"attackType_" : "melee",
					"rank": 1,

					"dontUseEnergy" : true,
					"cd" : -1,
					"returnToStartAfterAttack": true,

					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "range_attack", "normal_damage", "basic_attack", "armed_ability", "armed_ability_assassin" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"tempSelfBuff": "assassin_selfbuff_3",

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 0.5,
						
						"targetMechanics" : [ ]
					}
				}
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,
						
						"targetMechanics" : [ ]
					}
				}
			],
				
			"abilities" : 
			[ 
				{
					"disableForAi": true,
					"attackType_" : "cast",
					"rank": 4,

					"selfMechanics" : [ ],

					"cd" : 2,
					"energyLevel": 2,

					"damageDealer" : 
					{
						"instacast": true,
						"multitargetType": "simultaneous",
						
						"tags": [ "ability", "buff_ability" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_buff",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "self",
							"targetCondition_": "alive"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "self",
							"targetCondition_": "alive"
						},

						"damageTarget_": "none",

						"buffTarget_": "self",
						"buff":
						{
							"sid": "assassin_buff",
							"duration": 1
						},
						
						"targetMechanics" : [ ]
					}
				}
			]
		}
	]
}