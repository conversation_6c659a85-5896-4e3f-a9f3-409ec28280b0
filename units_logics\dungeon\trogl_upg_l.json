﻿{
	"array":
	[
		{
			"id": "trogl_upg",
			"squadValue": 48,
			"expBonus": 4,
			"tier": 1,
			"fraction": "dungeon",
			"nativeBiome": "Dirt",

			"ai": "melee_type",
			
			"tags": [ "unit", "dungeon" ],

			"baseSid": "trogl",
			"upgradeSid": "trogl_upg_alt",

			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 60 }
				]
			},
			
			"stats":
			{
				"hp": 8,
				"offence": 3,
				"defence": 5,
				"damageMin": 1,
				"damageMax": 3,

				"initiative": 6,
				"speed": 3,
				
				"luck": 0,
				"moral": 0,

				"energyPerCast": 2,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": -3,
				"moralMax": 3,
				"luckMin": -5,
				"luckMax": 5,

				"inDmgMods":
				{
					"list":
					[				
						
					]
				},

				"outDmgMods":
				{
					"list":
					[
									
					]
				}
			},
			
			"passives" : 
			[
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "cast", "tags": [ "magic_creature_immunities" ] }
						]
					}
				}
			],
			
			"defaultAttacks": 
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"triggerCounter" : true,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"buffTarget_": "enemy",
						"buff":
						{
							"sid": "trogl_debuff",
							"duration": 2
						},

						"targetMechanics" : [ ]
					}
				} 
			],
				
			"alternativeAttacks" : 
			[ 
				{
					"attackType_" : "range",
					"rank": 1,

					"dontUseEnergy" : true,
					"cd" : -1,
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "range_attack", "normal_damage", "basic_attack", "armed_ability", "armed_ability_trogl" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 0.5,
						
						"targetMechanics" : [ ]
					}
				}
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"buffTarget_": "enemy",
						"buff":
						{
							"sid": "trogl_debuff",
							"duration": 2
						},
						
						"targetMechanics" : [ ]
					}
				}
			],
				
			"abilities" : 
			[ 

			]
		}
	]
}