﻿{
	"array":
	[
		{
			"id": "mech_guard",
			"squadValue": 1650,
			"expBonus": 165,
			"tier": 6,
			"fraction": "neutral",

			"ai": "range_type",
			
			"tags": [ "unit", "neutral" ],

			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 1500 }
				]
			},

			"stats":
			{
				"hp": 75,
				"offence": 25,
				"defence": 25,
				"damageMin": 25,
				"damageMax": 25,

				"initiative": 6,
				"speed": 2,
				
				"luck": 0,
				"moral": 0,

				"energyPerCast": 1,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": 0,
				"moralMax": 0,
				"luckMin": 0,
				"luckMax": 0,

				"inDmgMods": 
				{
					"list":
					[
						{ "t": "magic_damage", "v": -0.9 }
					]
				},

				"outDmgMods": 
				{
					"list": 
					[ 

					]
				},

				"ignoreShootDmgBuff": true
			},

			"passives": 
			[
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "cast", "tags": [ "construct_immunities" ] }
						]
					}
				},
				{
					"data": 
					{
						"disablers": 
						[ 
							{ "mech": "vampirism" },
							{ "mech": "destroy_corpse_and_heal" }
						]
					}
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "effect", "tags": [ "overTime_effect_curse" ] },
							{ "type": "effect", "tags": [ "overTime_effect_blood" ] },
							{ "type": "effect", "tags": [ "overTime_effect_poison" ] },
							{ "type": "effect", "tags": [ "buff_effect_poison" ] }
						]
					}
				}
			],

			"defaultAttacks": 
			[
				{
					"attackType_" : "shoot",
					
					"selfMechanics" : [ ],
					
					"damageDealer" : 
					{
						"shootRange": -1,
						"shootThreshold": 3,
						"shootRedCount": 5,
						"shootDmgBuff": -0.0,
			
						"tags": [ "shoot_attack", "normal_damage", "basic_attack" ],
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,
						
						"targetMechanics" : [ ]
					}
				}
			],
				
			"alternativeAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				}
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				}
			],
					
			"abilities" : 
			[
				{
					"attackType_" : "cast",
					"rank": 4,
					
					"selfMechanics" : [ ],
															
					"cd" : 2,
					"energyLevel": 2,
					
					"damageDealer" : 
					{
						"tags": [  ],
						"triggerCounter": false,

						"castTargetParams":   
						{
							"castTarget_": "all",
							"selection": "hex"
						},

						"affectTargetParams":   
						{
							"castTarget_": "all",
							"selection": "hex"
						},
						
						"attackPatternSid": "attack_single_x100",
						"targetMechanics" : [ { "mech": "spawn_object", "values": [ "trap", "mech_guard_trap", "2", "1", "0", "true" ] } ]
					}
				}
			]
		}
	]
}