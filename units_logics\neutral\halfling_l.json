﻿{
	"array":
	[
		{
			"id": "halfling",
			"squadValue": 150,
			"expBonus": 15,
			"tier": 2,
			"fraction": "neutral",

			"ai": "range_type",
			
			"tags": [ "unit", "neutral" ],

			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 150 }
				]
			},

			"stats":
			{
				"hp": 6,
				"offence": 3,
				"defence": 3,
				"damageMin": 3,
				"damageMax": 3,

				"initiative": 6,
				"speed": 6,
				
				"luck": 0,
				"moral": 3,

				"energyPerCast": 1,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": 0,
				"moralMax": 0,
				"luckMin": 0,
				"luckMax": 0,

				"inDmgMods": 
				{
					"list":
					[
						
					]
				},

				"outDmgMods": 
				{
					"list": 
					[ 

					]
				}
			},

			"passives": 
			[
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "cast", "tags": [ "living_immunities" ] }
						]
					}
				}
			],

			"defaultAttacks": 
			[
				{
					"attackType_" : "shoot",
					
					"selfMechanics" : [ ],
					
					"damageDealer" : 
					{
						"shootRange": -1,
						"shootThreshold": 3,
						"shootRedCount": 5,
						"shootDmgBuff": -0.0,
			
						"tags": [ "shoot_attack", "normal_damage", "basic_attack" ],
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,
						
						"targetMechanics" : [ ]
					}
				}
			],
				
			"alternativeAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				}
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				}
			],
					
			"abilities" : 
			[
				{
					"attackType_" : "end_move_attack",
					"moveType": "teleport",
					"rank": 2,
					
					"selfMechanics" : [ ],
					
					"cd" : 1,
					"actionCost": 0,
					"energyLevel": 1,
					
					"damageDealer" : 
					{
						"tags": [ "ability", "movement_ability" ],

						"useSpeedAsRange": true,

						"triggerCounter": false,
						"attackPatternSid" : "attack_single_buff",

						"castTargetParams":   
						{ 
							"targetTags": [  ],
							"castTarget_": "none",
							"selection": "hex"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "noself",
							"selection": "object"
						},

						"damageTarget_": "none",

						"targetMechanics" : [ ]
					}
				}
			]
		}
	]
}