﻿{
	"array":
	[

		{
			"id": "lightweaver_upg_alt",
			"squadValue": 385,
			"expBonus": 38,
			"tier": 4,
			"fraction": "human",
			"nativeBiome": "Grass",

			"ai": "range_type",
			
			"tags": [ "unit", "human" ],

			"baseSid": "lightweaver_upg",

			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 340 }
				]
			},

			"stats":
			{	
				"hp": 25,
				"offence": 14,
				"defence": 11,
				"damageMin": 8,
				"damageMax": 16,

				"initiative": 5,
				"speed": 3,
				
				"luck": 0,
				"moral": 1,

				"energyPerCast": 1,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": -5,
				"moralMax": 5,
				"luckMin": -3,
				"luckMax": 3,

				"inDmgMods":
				{
					"list":
					[
						
					]
				},

				"outDmgMods":
				{
					"list":
					[
									
					]
				}
			}, 
			
			"passives" : 
			[
				{
					"actions": 
					[
						{
							"trigger": "unit_got_buff",
							"triggerConditions": 
							[
								{
									"checkFunction": "allegiance_receiving_tags",
									"values": [ "self", "receiving", "positive" ]
								}
							],
							"damageDealer": 
							{
								"attackPatternSid": "attack_single_x100",

								"castTargetParams":   
								{ 
									"targetTags": [ "unit" ],
									"castTarget_": "all",
									"selection": "object"
								},

								"affectTargetParams":   
								{ 
									"targetTags": [ "unit" ],
									"castTarget_": "all",
									"selection": "object"
								},
								
								"buffTarget_": "ally",
								"buff": 
								{
									"sid": "lightweaver_additionbuff",
									"duration": 1
								}
							}
						}
					]
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "cast", "tags": [ "living_immunities" ] }
						]
					}
				}
			],
			
			"defaultAttacks": 
			[
				{
					"attackType_" : "shoot",
					
					"damageDealer" : 
					{
						"shootRange": -1,
						"shootThreshold": 3,
						"shootRedCount": 5,
						"shootDmgBuff": -0.1,
			
						"tags": [ "shoot_attack", "normal_damage", "basic_attack" ],
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,
						
						"targetMechanics" : [ ]
					}
				}
			],
				
			"alternativeAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 0.5,

						"targetMechanics" : [ ]
					}
				}
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 0.5,

						"targetMechanics" : [ ]
					}
				}
			],
				
			"abilities": 
			[
				{
					"attackType_" : "cast",
					"rank": 4,
					
					"selfMechanics" : [ ],

					"cd" : 1,
					"energyLevel": 1,
					
					"damageDealer" : 
					{
						"targetCondition_": "alive",
						
						"tags": [ "ability", "buff_ability" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_buff",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "allynoself",
							"targetCondition_": "alive"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "allynoself",
							"targetCondition_": "alive"
						},

						"damageTarget_": "none",

						"buffTarget_": "ally",
						"buff":
						{
							"sid": "lightweaver_buff_2",
							"duration": 1
						},

						"targetMechanics" : [ ]
					}
				}
			]
		}
	]
}