import os

def main():
    """
    Основная функция скрипта
    """
    input_file = '15day.log'
    output_file = 'log15.txt'
    
    print(f"Обработка файла: {input_file}")
    
    # Проверяем существование файла
    if not os.path.exists(input_file):
        print(f"Ошибка: Файл {input_file} не найден!")
        return
    
    # Открываем файл и читаем все строки
    with open(input_file, 'r', encoding='utf-8') as file:
        lines = file.readlines()
        # если строка содержит 79 сохраняем
        for line in lines:
            if 'Start new day:' in line:
                print(line)
            if 'hero:79' in line or 'heroId:79' in line:
                print(line)
            if 'hire' in line:
                print(line)
if __name__ == "__main__":
    main()
