﻿{
	"array":
	[
		{
			"id": "vampire",
			"squadValue": 1981,
			"expBonus": 198,
			"tier": 7,
			"fraction": "undead",
			"nativeBiome": "Deathland",

			"ai": "melee_type",
			
			"tags": [ "unit", "undead" ],

			"upgradeSid": "vampire_upg",

			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 1200 },
					{ "name": "mercury", "cost": 1 }
				]
			},
			
			"stats":
			{
				"hp": 150,
				"offence": 25,
				"defence": 23,
				"damageMin": 25,
				"damageMax": 25,

				"initiative": 8,
				"speed": 5,
				
				"luck": 0,
				"moral": 0,

				"energyPerCast": 2,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": 0,
				"moralMax": 0,
				"luckMin": -5,
				"luckMax": 5,

				"inDmgMods":
				{
					"list":
					[
						
					]
				},

				"outDmgMods":
				{
					"list":
					[
									
					]
				},
				
				"moveType":"fly"
			},
			
			"passives" : 
			[
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "cast", "tags": [ "undead_immunities" ] }
						]
					}
				},
				{
					"data": 
					{
						"disablers": 
						[ 
							{ "mech": "vampirism" },
							{ "mech": "destroy_corpse_and_heal" }
						]
					}
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "effect", "tags": [ "overTime_effect_curse" ] },
							{ "type": "effect", "tags": [ "overTime_effect_blood" ] },
							{ "type": "effect", "tags": [ "overTime_effect_poison" ] },
							{ "type": "effect", "tags": [ "buff_effect_poison" ] }
						]
					}
				}
			],

			"defaultAttacks": 
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : 
					[
						{ 
							"mech": "vampirism", "values": [ "0" , "0.4" , "self" , "until start amount" ], "afterAttack": true 
						}
					],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"triggerCounter" : true,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				} 
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : 
					[
						{ 
							"mech": "vampirism", "values": [ "0" , "0.4" , "self" , "until start amount" ], "afterAttack": true 
						}
					],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter" : false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				} 
			],
				
			"abilities": [ ]
		}
	]
}