{"array": [{"value": 1500, "rewardSet": {"rewards": [{"rewardType": "HeroBoxUnitsReward", "parameters": ["lich", "flicker", "avatar_of_war"]}]}}, {"value": 2000, "rewardSet": {"rewards": [{"rewardType": "MovePointsAdditionReward", "parameters": ["300"]}]}}, {"value": 2000, "rewardSet": {"rewards": [{"rewardType": "ManaAdditionReward", "parameters": ["1000", "true"]}]}}, {"value": 2000, "rewardSet": {"rewards": [{"rewardType": "ManaPercentSettingReward", "parameters": ["0.8"]}]}}, {"value": 2000, "rewardSet": {"rewards": [{"rewardType": "HeroMagicAdditionReward", "parameters": ["night_2_magic_web", "primal_1_magic_thunderbolt", "primal_4_magic_fire_globe", "day_1_magic_healing_water", "day_8_magic_taunt", "primal_7_magic_wall_of_flame", "night_11_magic_vulnerability", "day_12_magic_radiant_armor", "night_16_magic_shadow_army"]}]}}, {"value": 2000, "rewardSet": {"rewards": [{"rewardType": "SideExpReward", "parameters": ["1000"]}]}}, {"value": 2000, "rewardSet": {"rewards": [{"rewardType": "SideExpToLevelUpReward", "parameters": ["false"]}]}}, {"value": 2000, "rewardSet": {"rewards": [{"rewardType": "HeroRandomItemsReward", "parameters": ["common"]}]}}, {"value": 5000, "rewardSet": {"rewards": [{"rewardType": "SideResReward", "parameters": ["gold", "5000"]}]}}, {"value": 5000, "rewardSet": {"rewards": [{"rewardType": "HeroBoxUnitReward", "parameters": ["1"]}]}}, {"value": 5000, "rewardSet": {"rewards": [{"rewardType": "HeroMagicMassAdditionReward", "parameters": ["any", "any", "1"]}]}}, {"value": 6000, "rewardSet": {"rewards": [{"rewardType": "HeroExpReward", "parameters": ["5000"]}]}}, {"value": 7500, "rewardSet": {"rewards": [{"rewardType": "HeroBoxUnitReward", "parameters": ["2"]}]}}, {"value": 8000, "rewardSet": {"rewards": [{"rewardType": "HeroStatsReward", "parameters": ["offence", "1", "defence", "1", "spellPower", "1", "intelligence", "1"]}]}}, {"value": 10000, "rewardSet": {"rewards": [{"rewardType": "SideResReward", "parameters": ["gold", "10000"]}]}}, {"value": 10000, "rewardSet": {"rewards": [{"rewardType": "HeroMagicMassAdditionReward", "parameters": ["any", "any", "2"]}]}}, {"value": 10500, "rewardSet": {"rewards": [{"rewardType": "HeroBoxUnitReward", "parameters": ["3"]}]}}, {"value": 12000, "rewardSet": {"rewards": [{"rewardType": "HeroExpReward", "parameters": ["10000"]}]}}, {"value": 14000, "rewardSet": {"rewards": [{"rewardType": "HeroBoxUnitReward", "parameters": ["4"]}]}}, {"value": 15000, "rewardSet": {"rewards": [{"rewardType": "SideResReward", "parameters": ["gold", "15000"]}]}}, {"value": 15000, "rewardSet": {"rewards": [{"rewardType": "HeroMagicMassAdditionReward", "parameters": ["any", "any", "3"]}]}}, {"value": 16000, "rewardSet": {"rewards": [{"rewardType": "HeroStatsReward", "parameters": ["offence", "2", "defence", "2", "spellPower", "2", "intelligence", "2"]}]}}, {"value": 18000, "rewardSet": {"rewards": [{"rewardType": "HeroExpReward", "parameters": ["15000"]}]}}, {"value": 18000, "rewardSet": {"rewards": [{"rewardType": "HeroBoxUnitReward", "parameters": ["5"]}]}}, {"value": 20000, "rewardSet": {"rewards": [{"rewardType": "SideResReward", "parameters": ["gold", "20000"]}]}}, {"value": 20000, "rewardSet": {"rewards": [{"rewardType": "HeroMagicMassAdditionReward", "parameters": ["day", "any", "any"]}]}}, {"value": 20000, "rewardSet": {"rewards": [{"rewardType": "HeroMagicMassAdditionReward", "parameters": ["night", "any", "any"]}]}}, {"value": 20000, "rewardSet": {"rewards": [{"rewardType": "HeroMagicMassAdditionReward", "parameters": ["space", "any", "any"]}]}}, {"value": 20000, "rewardSet": {"rewards": [{"rewardType": "HeroMagicMassAdditionReward", "parameters": ["primal", "any", "any"]}]}}, {"value": 20000, "rewardSet": {"rewards": [{"rewardType": "HeroMagicMassAdditionReward", "parameters": ["any", "any", "4"]}]}}, {"value": 22500, "rewardSet": {"rewards": [{"rewardType": "HeroBoxUnitReward", "parameters": ["6"]}]}}, {"value": 24000, "rewardSet": {"rewards": [{"rewardType": "HeroExpReward", "parameters": ["20000"]}]}}, {"value": 24000, "rewardSet": {"rewards": [{"rewardType": "HeroStatsReward", "parameters": ["offence", "3", "defence", "3", "spellPower", "3", "intelligence", "3"]}]}}, {"value": 25000, "rewardSet": {"rewards": [{"rewardType": "HeroMagicMassAdditionReward", "parameters": ["any", "any", "5"]}]}}, {"value": 27500, "rewardSet": {"rewards": [{"rewardType": "HeroBoxUnitReward", "parameters": ["7"]}]}}, {"value": 32000, "rewardSet": {"rewards": [{"rewardType": "HeroStatsReward", "parameters": ["offence", "4", "defence", "4", "spellPower", "4", "intelligence", "4"]}]}}]}