﻿{
	"array":
	[

		{
			"id": "angel",
			"squadValue": 3973,
			"expBonus": 397,
			"tier": 7,
			"fraction": "human",
			"nativeBiome": "Grass",

			"ai": "range_type",
			
			"tags": [ "unit", "human" ],

			"upgradeSid": "angel_upg",

			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 2400 },
					{ "name": "gemstones", "cost": 1  }
				]
			},

			"stats":
			{	
				"hp": 200,
				"offence": 30,
				"defence": 30,
				"damageMin": 50,
				"damageMax": 75,

				"initiative": 6,
				"speed": 4,
				
				"luck": 0,
				"moral": 0,

				"energyPerCast": 1,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": -3,
				"moralMax": 3,
				"luckMin": -3,
				"luckMax": 3,

				"inDmgMods":
				{
					"list":
					[
						
					]
				},

				"outDmgMods":
				{
					"list":
					[
									
					]
				},
				
				"moveType": "fly"
			},
			
			"passives" : 
			[
				{
					"actions":
					[
						{
							"trigger": "unit_got_buff",
							"triggerConditions": 
							[
								{
									"checkFunction": "allegiance",
									"values": [ "ally" ]
								}
							],

							"damageDealer": 
							{
								"tags": [ "passive" ],
								"triggerCounter": false,

								"damageTarget_": "none",
								"statDmgMult": 0.0,

								"attackPatternSid": "attack_single_x100",

								"castTargetParams":   
								{ 
									"targetTags": [ "unit" ],
									"castTarget_": "ally",
									"targetCondition_": "alive"
								},

								"affectTargetParams":   
								{ 
									"targetTags": [ "unit" ],
									"castTarget_": "ally",
									"targetCondition_": "alive"
								},

								"targetMechanics":
								[
									{ "mech": "mimic_buffs", "values": [ "0", "ally", "positive"] }
								]
							}
						}
					]
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "cast", "tags": [ "embodiment_immunities" ] }
						]
					}
				},
				{
					"data": 
					{
						"disablers": 
						[ 
							{ "mech": "vampirism" },
							{ "mech": "destroy_corpse_and_heal" }
						]
					}
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "effect", "tags": [ "overTime_effect_blood" ] },
							{ "type": "effect", "tags": [ "overTime_effect_poison" ] },
							{ "type": "effect", "tags": [ "buff_effect_poison" ] }
						]
					}
				}
			],

			"defaultAttacks": 
			[
				{
					"attackType_" : "shoot",
					
					"selfMechanics" : [ ],
					
					"damageDealer" : 
					{
						"shootRange": -1,
						"shootThreshold": 3,
						"shootRedCount": 5,
						"shootDmgBuff": -0.1,
			
						"tags": [ "shoot_attack", "normal_damage", "basic_attack" ],
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,
						
						"targetMechanics" : [ ]
					}
				}
			],
				
			"alternativeAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 0.5,

						"targetMechanics" : [ ]
					}
				}
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 0.5,

						"targetMechanics" : [ ]
					}
				}
			],
				
			"abilities": 
			[ 
				{
					"disableForAi": true,
					"attackType_" : "cast",
					"rank": 2,
					
					"selfMechanics" : [ ],

					"cd" : 1,
					"energyLevel": 1,

					"damageDealer" : 
					{
						"tags": [ "ability", "buff_ability" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_buff",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "allynoself",
							"targetCondition_": "alive"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "allynoself",
							"targetCondition_": "alive"
						},

						"damageTarget_": "none",
						
						"targetMechanics" : 
						[ 
							{	"mech": "transfer_buffs", "values": [ "copy", "0", "positive" ] } 
						]
					}
				}
			]
		}
	]
}