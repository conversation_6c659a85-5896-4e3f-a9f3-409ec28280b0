﻿{
	"array":
	[
		{
			"id": "olgoi",
			"squadValue": 1384,
			"expBonus": 138,
			"tier": 6,
			"fraction": "demon",
			"nativeBiome": "Lava",

			"ai": "melee_type",
			
			"tags": [ "unit", "demon" ],

			"upgradeSid": "olgoi_upg",
			
			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 1100 }
				]
			},

			"stats":
			{	
				"hp": 120,
				"offence": 24,
				"defence": 24,
				"damageMin": 21,
				"damageMax": 23,

				"initiative": 5,
				"speed": 7,
				
				"luck": 1,
				"moral": 0,

				"energyPerCast": 2,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": -5,
				"moralMax": 5,
				"luckMin": -5,
				"luckMax": 5,

				"inDmgMods":
				{
					"list":
					[

					]
				},

				"outDmgMods":
				{
					"list":
					[
									
					]
				},

				"moveType" : "teleport"
			},
			
			"passives": 
			[
				{
					"actions":
					[
						{
							"trigger": "unit_end_move",
							"triggerConditions": 
							[
								{
									"checkFunction": "allegiance",
									"values": [ "self" ]
								}
							],

							"damageDealer": 
							{
								"tags": [ "passive", "undead_immunities", "embodiment_immunities", "construct_immunities" ],
								"triggerCounter": false,

								"damageTarget_": "none",
								"statDmgMult": 0.0,
								"buffTarget_":"none", 

								"attackPatternSid": "attack_single_x100",

								"castTargetParams":   
								{ 
									"targetTags": [ "unit" ],
									"castTarget_": "all",
									"targetCondition_": "dead",
									"selection": "object"
								},

								"affectTargetParams":   
								{ 
									"targetTags": [ "unit" ],
									"castTarget_": "all",
									"targetCondition_": "alive",
									"selection": "object"
								},

								"targetMechanics": 
								[ 
									{ "mech": "destroy_corpse_and_heal", "values": [ "all", "65" , "1" , "0" , "until start amount" ] } 
								]
							}
						}
					]
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "cast", "tags": [ "demon_immunities" ] }
						]
					}
				}
			],
			
			"globalPassives" : 
			[
				{
					"tag": "olgoi_aura",
					"target": "enemy",
					"power": 1,
					"data": 
					{
						"stats" :
						{
							"luck" : -1
						}
					}	
				}
			],
			
			"defaultAttacks": 
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"triggerCounter" : true,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				} 
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,
						
						"targetMechanics" : [ ]
					}
				}
			],
				
			"abilities" : [ ]
		}
	]
}