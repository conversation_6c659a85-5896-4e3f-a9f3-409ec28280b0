﻿{
	"array":
	[
		{
			"id": "vampire_upg_alt",
			"squadValue": 3447,
			"expBonus": 344,
			"tier": 7,
			"fraction": "undead",
			"nativeBiome": "Deathland",

			"ai": "reach_type",
			
			"tags": [ "unit", "undead" ],

			"baseSid": "vampire_upg",

			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 2000 },
					{ "name": "mercury", "cost": 2 }
				]
			},
			
			"stats":
			{
				"hp": 150,
				"offence": 30,
				"defence": 23,
				"damageMin": 30,
				"damageMax": 30,

				"initiative": 8,
				"speed": 5,
				
				"luck": 0,
				"moral": 0,

				"energyPerCast": 2,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": 0,
				"moralMax": 0,
				"luckMin": -5,
				"luckMax": 5,

				"inDmgMods":
				{
					"list":
					[
						
					]
				},

				"outDmgMods":
				{
					"list":
					[
									
					]
				},
				
				"moveType":"fly"
			},
			
			"passives" : 
			[
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "cast", "tags": [ "undead_immunities" ] }
						]
					}
				},
				{
					"data": 
					{
						"disablers": 
						[ 
							{ "mech": "vampirism" },
							{ "mech": "destroy_corpse_and_heal" }
						]
					}
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "effect", "tags": [ "overTime_effect_curse" ] },
							{ "type": "effect", "tags": [ "overTime_effect_blood" ] },
							{ "type": "effect", "tags": [ "overTime_effect_poison" ] },
							{ "type": "effect", "tags": [ "buff_effect_poison" ] }
						]
					}
				}
			],

			"defaultAttacks": 
			[
				{
					"attackType_" : "range",
							
					"selfMechanics" : 
					[
						{ 
							"mech": "vampirism", "values": [ "0" , "0.4" , "self" , "until start amount" ], "afterAttack": true 
						}
					],

					"damageDealer" : 
					{
						"tags": [ "range_attack", "normal_damage", "basic_attack" ],
						"triggerCounter" : true,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				} 
			],

			"alternativeAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : 
					[
						{ 
							"mech": "vampirism", "values": [ "0" , "0.4" , "self" , "until start amount" ], "afterAttack": true 
						}
					],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"triggerCounter" : true,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				}
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : 
					[
						{ 
							"mech": "vampirism", "values": [ "0" , "0.4" , "self" , "until start amount" ], "afterAttack": true 
						}
					],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter" : false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				} 
			],
				
			"abilities": 
			[
				{
					"attackType_" : "cast",

					"energyLevel": 3,
					"cd": 3,

					"damageDealer" : 
					{
						"tags": [ "cast_attack", "magic_damage", "ability", "embodiment_immunities", "construct_immunities" ],
						"triggerCounter" : false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},
						
						"multitargetType": "ordered",
						"numTargets": 2,

						"orderedCastParams":
						[
							{
								"targetTags": [ "unit", "attackable_object" ],
								"castTarget_": "ally",
								"targetCondition_": "alive",
								"selection": "object"
							}
						],

						"orderedAffectParams":
						[
							{
								"targetTags": [ "unit", "attackable_object" ],
								"castTarget_": "ally",
								"targetCondition_": "alive",
								"selection": "object"
							}
						],

						"damageTarget_": "none",
						"damageType_": "magic_pure",

						"targetMechanics" : 
						[
							{ "mech": "transfer_hp", "values": [ "0", "30", "1", "0.5", "until start amount"] }
						]
					}
				},
				{
					"disableForAi": true,
					"attackType_" : "cast",

					"energyLevel": 3,
					"cd": 3,

					"damageDealer" : 
					{
						"tags": [ "cast_attack", "magic_damage", "ability", "embodiment_immunities", "construct_immunities" ],
						"triggerCounter" : false,
						"attackPatternSid" : "attack_rumble_x1_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "all",
							"targetCondition_": "dead"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "all",
							"targetCondition_": "dead"
						},

						"damageTarget_": "none",
						"damageType_": "magic_pure",

						"targetMechanics" : 
						[
							{ "mech": "explode_corpse_with_heal", "values": [ "20", "1", "10", "1", "without respawn", "enemy", "ally"] }
						]
					}
				} 
			]
		}
	]
}