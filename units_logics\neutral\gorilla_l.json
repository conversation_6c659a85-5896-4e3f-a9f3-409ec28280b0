﻿{
	"array":
	[
		{
			"id": "gorilla",
			"squadValue": 250,
			"expBonus": 25,
			"tier": 3,
			"fraction": "neutral",

			"ai": "melee_type",
			
			"tags": [ "unit", "neutral" ],

			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 275 }
				]
			},

			"stats":
			{
				"hp": 40,
				"offence": 10,
				"defence": 8,
				"damageMin": 5,
				"damageMax": 9,

				"initiative": 6,
				"speed": 4,
				
				"luck": 0,
				"moral": 0,

				"energyPerCast": 2,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": -5,
				"moralMax": 5,
				"luckMin": -3,
				"luckMax": 3,

				"inDmgMods": 
				{
					"list": 
					[ 

					]
				},

				"outDmgMods": 
				{
					"list": 
					[ 

					]
				}
			},
			
			"passives": 
			[
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "cast", "tags": [ "living_immunities" ] }
						]
					}
				}
			],
			
			"defaultAttacks": 
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"triggerCounter" : true,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"targetMechanics" : [ ]
					}
				} 
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack", "counter_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,
						
						"targetMechanics" : [ ]
					}
				}
			],
				
			"abilities" : 
			[ 
				{
					"attackType_" : "jump",
					"rank": 2,
					
					"selfMechanics" : [ ],
																				
					"cd" : 2,
					"energyLevel": 2,
					
					"damageDealer" : 
					{	
						"shootRange": 4,

						"tags": [ "melee_attack", "normal_damage", "basic_attack", "movement_ability" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_swirl_x1_x100",

						"castTargetParams":
						{
							"targetTags": [ ],
							"castTarget_": "none",
							"selection": "hex"
						},
						
						"affectTargetParams":
						{
							"targetTags": [ "unit" ],
							"castTarget_": "all",
							"targetCondition_": "alive"
						},

						"damageTarget_": "noself",

						"targetMechanics" : [ ]
					}
				},
				{
					"attackType_" : "cast",
					"rank": 2,
					
					"selfMechanics" : [ ],
															
					"cd" : 3,
					"energyLevel": 3,

					"damageDealer" : 
					{
						"tags": [ "magic_damage", "ability" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "defense_structure" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "defense_structure" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive"
						},

						"damageTarget_": "enemy",
						"damageType_": "magic_pure",

						"buffTarget_": "enemy",
						"buff":
						{
							"sid": "gorilla_stun",
							"duration": 1
						},

						"statDmgMult": 1.5,

						"targetMechanics" : [ ]
					}
				}
			]
		}
	]
}