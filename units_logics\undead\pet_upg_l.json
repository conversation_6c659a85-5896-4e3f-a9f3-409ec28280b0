﻿{
	"array":
	[
		{
			"id": "pet_upg",
			"squadValue": 160,
			"expBonus": 16,
			"tier": 3,
			"fraction": "undead",
			"nativeBiome": "Deathland",

			"ai": "melee_type",
			
			"tags": [ "unit", "undead" ],

			"baseSid": "pet",
			"upgradeSid": "pet_upg_alt",

			"unitCost":
			{
				"costResArray":
				[
					{ "name": "gold", "cost": 150 }
				]
			},

			"stats":
			{	
				"hp": 18,
				"offence": 7,
				"defence": 7,
				"damageMin": 3,
				"damageMax": 5,

				"initiative": 4,
				"speed": 5,
				
				"luck": 0,
				"moral": 0,

				"energyPerCast": 2,
				"energyPerRound": 0,
				"energyPerTakeDamage": 1,
				
				"actionPoints": 1,
				"numCounters": 1,

				"moralMin": 0,
				"moralMax": 0,
				"luckMin": -5,
				"luckMax": 5,

				"inDmgMods":
				{
					"list":
					[
						{ "t": "melee_attack", "v": -0.3 }
					]
				},

				"outDmgMods":
				{
					"list":
					[
									
					]
				}
			},
			
			"passives" : 
			[
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "cast", "tags": [ "undead_immunities" ] }
						]
					}
				},
				{
					"data": 
					{
						"disablers": 
						[ 
							{ "mech": "vampirism" },
							{ "mech": "destroy_corpse_and_heal" }
						]
					}
				},
				{
					"data": 
					{
						"immunities": 
						[ 
							{ "type": "effect", "tags": [ "overTime_effect_curse" ] },
							{ "type": "effect", "tags": [ "overTime_effect_blood" ] },
							{ "type": "effect", "tags": [ "overTime_effect_poison" ] },
							{ "type": "effect", "tags": [ "buff_effect_poison" ] }
						]
					}
				},
				{
					"actions":
					[
						{
							"trigger": "unit_took_damage",
							"triggerConditions": 
							[
								{
									"checkFunction": "allegiance_both",
									"values": [ "self", "enemy" ]
								}
							],

							"damageDealer": 
							{
								"tags": [ "passive" ],
								"triggerCounter": false,

								"attackPatternSid": "attack_single_x100",

								"castTargetParams":   
								{ 
									"targetTags": [ "unit" ],
									"castTarget_": "self"
								},
		
								"affectTargetParams":   
								{ 
									"targetTags": [ "unit" ],
									"castTarget_": "self"
								},						

								"damageTarget_": "none",
								"statDmgMult": 0.0,
		
								"buffTarget_": "self",
								"buff":
								{
									"sid": "pet_buff",
									"duration": -1
								},
								"targetMechanics" : [ ]
							}
						}
					]
				}
			],

			"defaultAttacks": 
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"triggerCounter": true,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "enemy",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"buffTarget_": "enemy",
						"buff":
						{
							"sid": "pet_debuff",
							"duration": -1
						},

						"targetMechanics" : [ ]
					}
				} 
			],
			
			"counterAttacks":
			[
				{
					"attackType_" : "melee",
					
					"selfMechanics" : [ ],

					"damageDealer" : 
					{
						"tags": [ "melee_attack", "normal_damage", "basic_attack" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_x100",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit", "attackable_object" ],
							"castTarget_": "all",
							"targetCondition_": "alive",
							"selection": "object"
						},

						"damageTarget_": "enemy",
						"damageType_": "normal",

						"statDmgMult": 1.0,

						"buffTarget_": "enemy",
						"buff":
						{
							"sid": "pet_debuff",
							"duration": -1
						},

						"targetMechanics" : [ ]
					}
				}
			],
				
			"abilities": 
			[
				{
					"attackType_" : "cast",
					"rank": 4,
																				
					"cd" : 2,
					"energyLevel": 2,
					
					"selfMechanics" : 
					[ 
						{
							"mech": "heal_percent",	"values":	[ "with temp stacks",  "max_squad",  "0.2",  "0.00",  "true" ]
						}
					],

					"damageDealer" : 
					{
						"instacast": true,
						"multitargetType": "simultaneous",
						
						"tags": [ "ability" ],
						"triggerCounter": false,
						"attackPatternSid" : "attack_single_buff",

						"castTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "self",
							"targetCondition_": "alive"
						},

						"affectTargetParams":   
						{ 
							"targetTags": [ "unit" ],
							"castTarget_": "self",
							"targetCondition_": "alive"
						},

						"damageTarget_": "none",

						"targetMechanics" : [  ]
					}
				}
			]
		}
	]
}